"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[825],{89841:function(e,t,l){l.d(t,{Z:function(){return f}});var r=l(57437),s=l(2265),a=l(27648),n=l(32489),i=l(92369),o=l(83774),c=l(88997),d=l(68661),u=l(44995),g=l(28819),m=e=>{let{src:t,alt:l,className:a,placeholder:n,fallback:i="/images/placeholder.jpg",onLoad:o,onError:c,priority:u=!1,quality:m=75,sizes:f}=e,[h,x]=(0,s.useState)("loading"),[p,b]=(0,s.useState)(n||""),y=(0,s.useRef)(null),{ref:v,inView:w}=(0,g.YD)({threshold:0,rootMargin:"200px",triggerOnce:!0,skip:u}),j=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:75;if(!e)return e;if(e.includes(".myqcloud.com")||e.includes(".cos.")){let l=e.includes("?")?"&":"?";return"".concat(e).concat(l,"imageMogr2/quality/").concat(t,"/format/webp")}return e.includes(".tcloudbaseapp.com"),e},N=e=>new Promise((t,l)=>{let r=new Image;r.onload=()=>{x("loaded"),b(e),null==o||o(),t()},r.onerror=()=>{x("error"),b(i),null==c||c(),l(Error("Image load failed"))},r.src=e});(0,s.useEffect)(()=>{if(u||w){let e=j(t,m),l=performance.now();N(e).then(()=>{let e=performance.now()-l;console.log("图片加载完成: ".concat(t,", 耗时: ").concat(e.toFixed(2),"ms"))}).catch(()=>{e!==t?(console.warn("优化图片加载失败，尝试原图:",t),N(t).catch(()=>{console.error("原图加载也失败:",t),x("error"),b(i)})):(x("error"),b(i))})}},[w,u,t,m,i,o,c]);let k=(0,s.useCallback)(()=>{if(!u&&!w&&"loading"===h){let e=j(t,m);console.log("鼠标悬停预加载:",t),N(e).catch(()=>{})}},[u,w,h,t,m]);return(0,r.jsxs)("div",{ref:v,className:"relative overflow-hidden w-full h-full",onMouseEnter:k,children:["loading"===h&&!p&&(0,r.jsx)("div",{className:(0,d.cn)("bg-gray-200 animate-pulse flex items-center justify-center w-full h-full",a),children:(0,r.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z",clipRule:"evenodd"})})}),"error"===h&&(0,r.jsx)("div",{className:(0,d.cn)("bg-gray-100 flex items-center justify-center text-gray-400 w-full h-full",a),children:(0,r.jsx)("svg",{className:"w-8 h-8",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),("loaded"===h||p)&&(0,r.jsx)("img",{ref:y,src:p,alt:l,className:(0,d.cn)("transition-opacity duration-300","loaded"===h?"opacity-100":"opacity-0",a),sizes:f,loading:u?"eager":"lazy",decoding:"async"}),"loading"===h&&p&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gray-200 animate-pulse"})]})},f=e=>{var t,l,s;let{post:g,className:f,showRemoveFromFavorites:h=!1,onRemoveFromFavorites:x,isDraft:p=!1}=e,b=(null===(t=g.images)||void 0===t?void 0:t[0])||"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",y=(e=>{switch(e){case"breeding":return{label:"配种",color:"bg-pink-500"};case"selling":return{label:"出售",color:"bg-green-500"};case"lost":return{label:"寻回",color:"bg-orange-500"};case"wanted":return{label:"求购",color:"bg-purple-500"};default:return null}})(g.type);return(0,r.jsx)(a.default,{href:"/post/detail?id=".concat(g._id),children:(0,r.jsxs)("div",{className:(0,d.cn)("bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer h-full flex flex-col",f),children:[(0,r.jsxs)("div",{className:"relative w-full overflow-hidden",style:{height:"200px"},children:[(0,r.jsx)(m,{src:b,alt:g.breed||"宠物图片",className:"w-full h-full object-cover rounded-t-lg",fallback:"https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center",quality:80,sizes:"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"}),p&&(0,r.jsx)("div",{className:"absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium",children:"待发布"}),!p&&y&&(0,r.jsx)("div",{className:(0,d.cn)("absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",y.color),children:y.label}),g.images&&g.images.length>1&&(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full",children:["1/",g.images.length]}),h&&(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),null==x||x()},className:"absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors",title:"从收藏中移除",children:(0,r.jsx)(n.Z,{className:"h-3 w-3"})})]}),(0,r.jsx)("div",{className:"p-3 flex-1 flex flex-col justify-center",style:{minHeight:"72px"},children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-full",children:[(0,r.jsxs)("div",{className:"flex flex-col justify-center flex-1 min-w-0 pr-2",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 text-sm leading-tight truncate mb-1",children:g.breed||"未知品种"}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-600",children:[(0,r.jsx)(i.Z,{className:"h-3 w-3 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:(null===(l=g.user)||void 0===l?void 0:l.nickname)||(null===(s=g.author)||void 0===s?void 0:s.nickname)||"匿名用户"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col justify-center items-end flex-shrink-0",children:[g.location&&(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500 mb-1",children:[(0,r.jsx)(o.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"truncate max-w-20",title:g.location,children:(0,u.lx)(g.location)})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-500",children:[(0,r.jsx)(c.Z,{className:"h-3 w-3"}),(0,r.jsx)("span",{children:(0,d.uf)(g.likes_count||0)})]})]})]})})]})})}},56334:function(e,t,l){var r=l(57437),s=l(2265),a=l(68661);let n=s.forwardRef((e,t)=>{let{className:l,variant:s="primary",size:n="md",loading:i=!1,icon:o,children:c,disabled:d,...u}=e;return(0,r.jsxs)("button",{className:(0,a.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],l),ref:t,disabled:d||i,...u,children:[i&&(0,r.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!i&&o&&(0,r.jsx)("span",{className:"mr-2",children:o}),c]})});n.displayName="Button",t.Z=n},44995:function(e,t,l){function r(e){if(!e||"string"!=typeof e)return"";let t=e.replace(/^#/,"").trim();if(!t)return"";try{let l="",r="",s="",a=t.match(/(.*?(?:省|自治区|特别行政区))/);a&&(l=a[1],t=t.replace(l,""));let n=t.match(/(.*?市)/);n&&(r=n[1],t=t.replace(r,""));let i=t.match(/(.*?(?:县|区|市|旗|自治县|林区|特区))/);i&&(s=i[1]);let o="",c=["北京","上海","天津","重庆"].some(e=>l&&l.includes(e)||r&&r.includes(e)),d=s&&["浦东新区","滨海新区","两江新区"].some(e=>s.includes(e));if(c?d&&r?o=r+s:s?o=s:r?o=r:l&&(o=l):r&&s?o=r+s:r?o=r:s?o=s:l&&(o=l),!o){let t=e.replace(/^#/,"").trim(),l=t.match(/(.*?(?:镇|乡|街道|办事处))/);if(l)o=l[1];else{let e=t.match(/(.*?(?:村|社区|小区|路|街|巷|弄|号))/);if(e){let t=e[1];o=t.length>8?t.substring(0,8)+"...":t}else o=t.length>10?t.substring(0,10)+"...":t}}return o}catch(e){return console.error("地址格式化出错:",e),t.length>10?t.substring(0,10)+"...":t}}function s(e){return e&&"string"==typeof e?e.replace(/^#/,"").trim():""}l.d(t,{Ed:function(){return s},lx:function(){return r}})}}]);