"use strict";exports.id=792,exports.ids=[792],exports.modules={37202:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},9015:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},39730:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]])},42887:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},34565:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},1572:(e,s,t)=>{t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},91792:(e,s,t)=>{t.d(s,{Z:()=>T});var a=t(10326),r=t(17577),l=t(39730),n=t(79635),i=t(76557);let c=(0,i.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);var d=t(94019);let o=(0,i.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);var m=t(99837),x=t(35659),h=t(28676),p=t(89175);let u=(0,i.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var f=t(9015),g=t(74131),y=t(41828),j=t(28295);let b=({isOpen:e,onClose:s,onSuccess:t})=>{let{login:l,isLoading:n}=(0,g.a)(),[i,c]=(0,r.useState)("main"),[o,x]=(0,r.useState)({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),[b,v]=(0,r.useState)({}),[w,N]=(0,r.useState)(!1),[k,C]=(0,r.useState)(0),Z=()=>{x({email:"",password:"",nickname:"",confirmPassword:"",verificationCode:""}),v({}),c("main"),N(!1),C(0)},I=()=>{Z(),s()},S=()=>{let e={};return("email"===i||"register"===i)&&(o.email.trim()?(0,j.vV)(o.email)||(e.email="请输入有效的邮箱地址"):e.email="请输入邮箱地址",o.password.trim()?o.password.length<6&&(e.password="密码至少6位"):e.password="请输入密码"),"register"===i&&(o.nickname.trim()?o.nickname.length<2?e.nickname="昵称至少需要2个字符":o.nickname.length>20&&(e.nickname="昵称不能超过20个字符"):e.nickname="请输入昵称",o.confirmPassword.trim()?o.password!==o.confirmPassword&&(e.confirmPassword="两次密码输入不一致"):e.confirmPassword="请确认密码"),v(e),0===Object.keys(e).length},M=async()=>{console.log("微信登录")},P=async()=>{if(!o.email.trim()){v({email:"请输入邮箱地址"});return}if(!(0,j.vV)(o.email)){v({email:"请输入有效的邮箱地址"});return}try{N(!0);let e=await y.authAPI.sendVerificationCode(o.email,"register");if(e.success){C(60);let e=setInterval(()=>{C(s=>s<=1?(clearInterval(e),0):s-1)},1e3)}else v({verificationCode:e.message})}catch(e){v({verificationCode:e.message||"发送验证码失败"})}finally{N(!1)}},_=async()=>{S()&&await l(o.email,o.password)&&(t?.(),I())},$=async()=>{if(S())try{let e=await y.authAPI.registerWithEmail(o.email,o.password,o.nickname,o.verificationCode);e.success?await l(o.email,o.password)&&(t?.(),I()):v({verificationCode:e.message})}catch(e){v({verificationCode:e.message||"注册失败"})}},z=(e,s)=>{x(t=>({...t,[e]:s})),b[e]&&v(s=>({...s,[e]:""}))};return a.jsx(h.u_,{isOpen:e,onClose:I,size:"sm",children:(0,a.jsxs)(h.fe,{children:[a.jsx("button",{onClick:I,className:"absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx(d.Z,{className:"w-5 h-5"})}),"main"===i&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"欢迎回来"}),a.jsx("p",{className:"text-gray-600",children:"选择登录方式继续使用"})]}),(0,a.jsxs)(m.Z,{onClick:M,className:"w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg flex items-center justify-center space-x-2",disabled:n,children:[(0,a.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:[a.jsx("path",{d:"M8.691 2.188C3.891 2.188 0 5.476 0 9.53c0 2.212 1.17 4.203 3.002 5.55a.59.59 0 0 1 .213.665l-.39 1.48c-.019.07-.048.141-.048.213 0 .163.13.295.29.295a.326.326 0 0 0 .167-.054l1.903-1.114a.864.864 0 0 1 .717-.098 10.16 10.16 0 0 0 2.837.403c.276 0 .543-.027.811-.05-.857-2.578.157-4.972 1.932-6.446 1.703-1.415 4.882-1.932 7.621-.55-.302-2.676-2.91-4.624-6.364-4.624zm-2.44 5.738a.868.868 0 0 1-.869-.855c0-.472.39-.856.869-.856s.868.384.868.856a.868.868 0 0 1-.868.855zm4.928 0a.868.868 0 0 1-.868-.855c0-.472.39-.856.868-.856s.869.384.869.856a.868.868 0 0 1-.869.855z"}),a.jsx("path",{d:"M24 14.388c0-3.14-2.956-5.69-6.594-5.69-3.638 0-6.594 2.55-6.594 5.69 0 3.14 2.956 5.69 6.594 5.69a7.842 7.842 0 0 0 2.208-.32.671.671 0 0 1 .556.075l1.462.855a.25.25 0 0 0 .128.042.226.226 0 0 0 .223-.227.166.166 0 0 0-.037-.164l-.3-1.14a.454.454 0 0 1 .164-.512C22.84 17.64 24 16.125 24 14.388zm-8.738-1.14a.67.67 0 0 1-.669-.66c0-.365.3-.66.67-.66a.67.67 0 0 1 .668.66c0 .365-.3.66-.669.66zm3.348 0a.67.67 0 0 1-.668-.66c0-.365.3-.66.668-.66a.67.67 0 0 1 .669.66c0 .365-.3.66-.669.66z"})]}),a.jsx("span",{children:"微信登录"})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-sm",children:a.jsx("span",{className:"px-2 bg-white text-gray-500",children:"或"})})]}),(0,a.jsxs)(m.Z,{onClick:()=>c("email"),variant:"outline",className:"w-full py-3 rounded-lg flex items-center justify-center space-x-2",children:[a.jsx(u,{className:"w-5 h-5"}),a.jsx("span",{children:"邮箱登录"})]})]}),"email"===i&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("button",{onClick:()=>c("main"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"邮箱登录"}),a.jsx("p",{className:"text-gray-600",children:"使用邮箱和密码登录"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(p.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:o.email,onChange:e=>z("email",e.target.value),error:b.email,leftIcon:a.jsx(u,{className:"h-4 w-4"}),disabled:n}),a.jsx(p.I,{label:"密码",type:"password",placeholder:"请输入密码",value:o.password,onChange:e=>z("password",e.target.value),error:b.password,leftIcon:a.jsx(f.Z,{className:"h-4 w-4"}),disabled:n})]}),a.jsx(m.Z,{onClick:_,className:"w-full py-3 rounded-lg",disabled:n,children:n?"登录中...":"登录"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("span",{className:"text-gray-600",children:"还没有账号？"}),a.jsx("button",{onClick:()=>c("register"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即注册"})]})]}),"register"===i&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("button",{onClick:()=>c("email"),className:"absolute left-4 top-4 text-gray-400 hover:text-gray-600 transition-colors",children:a.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"注册账号"}),a.jsx("p",{className:"text-gray-600",children:"创建您的宠物交易账号"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx(p.I,{label:"邮箱地址",type:"email",placeholder:"请输入邮箱地址",value:o.email,onChange:e=>z("email",e.target.value),error:b.email,leftIcon:a.jsx(u,{className:"h-4 w-4"}),disabled:n}),a.jsx(p.I,{label:"昵称",type:"text",placeholder:"请输入昵称",value:o.nickname,onChange:e=>z("nickname",e.target.value),error:b.nickname,disabled:n,maxLength:20}),a.jsx(p.I,{label:"密码",type:"password",placeholder:"请输入密码（至少6位）",value:o.password,onChange:e=>z("password",e.target.value),error:b.password,leftIcon:a.jsx(f.Z,{className:"h-4 w-4"}),disabled:n}),a.jsx(p.I,{label:"确认密码",type:"password",placeholder:"请再次输入密码",value:o.confirmPassword,onChange:e=>z("confirmPassword",e.target.value),error:b.confirmPassword,leftIcon:a.jsx(f.Z,{className:"h-4 w-4"}),disabled:n}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"邮箱验证码"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(p.I,{type:"text",placeholder:"请输入6位验证码",value:o.verificationCode,onChange:e=>z("verificationCode",e.target.value),error:b.verificationCode,disabled:n,maxLength:6,className:"flex-1"}),a.jsx(m.Z,{onClick:P,disabled:w||k>0||!o.email||!(0,j.vV)(o.email),variant:"outline",className:"whitespace-nowrap",children:w?"发送中...":k>0?`${k}s`:"发送验证码"})]})]})]}),a.jsx(m.Z,{onClick:$,className:"w-full py-3 rounded-lg",disabled:n,children:n?"注册中...":"注册"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("span",{className:"text-gray-600",children:"已有账号？"}),a.jsx("button",{onClick:()=>c("email"),className:"text-blue-600 hover:text-blue-700 ml-1",children:"立即登录"})]})]})]})})};var v=t(90434),w=t(1572);let N=()=>{let[e,s]=(0,r.useState)([]),[t,l]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{(async()=>{try{let e=await y.activityAPI.getActiveActivities();e.success&&e.data.length>0&&(s(e.data),l(!0))}catch(e){console.error("获取活动失败:",e)}})()},[]),t&&0!==e.length)?a.jsx("div",{className:"hidden md:flex items-center",children:(0,a.jsxs)("button",{onClick:()=>{window.location.href="/activities"},className:"flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-full text-sm font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md",children:[a.jsx(w.Z,{className:"h-4 w-4"}),a.jsx("span",{className:"max-w-32 truncate",children:e.length>1?`${e.length}个活动`:e[0]?.title})]})}):null};t(42887);var k=t(34565),C=t(67427),Z=t(88307),I=t(88378),S=t(37202),M=t(54659),P=t(13835);let _=({isOpen:e,onClose:s})=>{let{user:t,isLoggedIn:n}=(0,x.E)(),[i,c]=(0,r.useState)([]),[o,m]=(0,r.useState)(!1),[h,p]=(0,r.useState)("trade"),[u,f]=(0,r.useState)(null),[g,j]=(0,r.useState)(null),b=async()=>{if(n&&e)try{m(!0);let e=await y.petAPI.getUserNotifications({limit:50,type:"system"===h?void 0:"contact"});if(e.success){let s=(e.data||[]).map(e=>{if("contact"===e.type){let s=e.sender_id===t?.uid;return{id:e._id,type:"contact",fromUser:{id:e.sender_id,nickname:e.data?.sender_nickname||e.data?.author_nickname||"用户",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"},toUser:{id:e.recipient_id,nickname:t?.nickname||"",avatar:t?.avatar_url||""},postInfo:{id:e.post_id,title:e.data?.post_title||"宠物帖子",image:"https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop",postType:e.data?.post_type||"selling"},contactInfo:{method:e.data?.sender_contact?.type||e.data?.author_contact?.type||"wechat",value:e.data?.sender_contact?.value||e.data?.author_contact?.value||"未提供"},timestamp:e.created_at,read:e.read||!1,isInitiator:s}}return"system"===e.type||"punishment"===e.type||"appeal"===e.type?{id:e._id,type:"system",title:e.message,content:e.message,timestamp:e.created_at,read:e.read||!1,severity:"punishment"===e.type?"warning":"info",canAppeal:"punishment"===e.type,reportId:e.data?.reportId,appealStatus:e.data?.appealStatus||"none",notificationType:e.data?.notificationType||"general",postTitle:e.data?.postTitle,reportReason:e.data?.reportReason}:null}).filter(Boolean);c(s)}else c([])}catch(e){console.error("加载通知失败:",e),c([])}finally{m(!1)}};(0,r.useEffect)(()=>{e&&b()},[e,n,h]);let v=async e=>{try{await y.petAPI.markNotificationRead({notificationId:e}),c(s=>s.map(s=>s.id===e?{...s,read:!0}:s))}catch(e){console.error("标记已读失败:",e)}},w=async e=>{if(!u){f(e);try{await y.petAPI.deleteNotification({notificationId:e}),c(s=>s.filter(s=>s.id!==e))}catch(e){console.error("删除失败:",e),j({title:"操作失败",message:"删除失败，请稍后重试",onConfirm:()=>j(null),confirmText:"确定",showCancel:!1,showIcon:!1})}finally{f(null)}}},N=async()=>{j({title:"清空通知",message:{trade:"确定清空所有买卖通知吗？",breeding:"确定清空所有配种通知吗？",lost:"确定清空所有寻回通知吗？",system:"确定清空所有系统通知吗？"}[h],showIcon:!1,onConfirm:async()=>{try{let e=S.map(e=>e.id);if(0===e.length)return;await y.petAPI.bulkDeleteNotifications({notificationIds:e}),c(s=>s.filter(s=>!e.includes(s.id))),console.log(`已清空${S.length}条通知`)}catch(e){console.error("清空失败:",e),j({title:"操作失败",message:"清空失败，请稍后重试",onConfirm:()=>j(null),confirmText:"确定",showCancel:!1,showIcon:!1})}}})},S=i.filter(e=>"system"===h?"system"===e.type:"breeding"===h?"contact"===e.type&&"breeding"===e.postInfo.postType:"lost"===h?"contact"===e.type&&"lost"===e.postInfo.postType:"trade"!==h||"contact"===e.type&&("selling"===e.postInfo.postType||"wanted"===e.postInfo.postType));return e?(0,a.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",onClick:s,children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0",children:[a.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"通知中心"}),a.jsx("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-500"})})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1 m-4 flex-shrink-0",children:[(0,a.jsxs)("button",{onClick:()=>p("trade"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"trade"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(k.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"买卖通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("breeding"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"breeding"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(C.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"配种通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("lost"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"lost"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(Z.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"寻回通知"})]}),(0,a.jsxs)("button",{onClick:()=>p("system"),className:`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"system"===h?"bg-white text-blue-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[a.jsx(I.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"系统通知"})]})]}),a.jsx("div",{className:"flex-1 overflow-y-auto p-6",children:o?(0,a.jsxs)("div",{className:"flex items-center justify-center py-8",children:[a.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"}),a.jsx("span",{className:"ml-2 text-gray-600",children:"加载中..."})]}):0===S.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(l.Z,{className:"h-12 w-12 text-gray-300 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-500",children:"暂无通知"})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"flex justify-end mb-4",children:a.jsx("button",{onClick:N,className:"px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors",children:"清空当前分类"})}),a.jsx("div",{className:"space-y-4",children:S.map(e=>a.jsx("div",{className:`bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ${e.read?"border-gray-200":"border-blue-200 bg-blue-50"}`,onClick:()=>v(e.id),children:"system"===e.type?a.jsx(z,{message:e,onDelete:w,deletingId:u}):a.jsx($,{message:e,onDelete:w,deletingId:u})},e.id))})]})})]}),g&&a.jsx(P.Z,{isOpen:!0,onClose:()=>j(null),onConfirm:()=>{g.onConfirm(),!1!==g.showCancel&&j(null)},title:g.title,message:g.message,confirmText:g.confirmText||"确定",type:"info",showIcon:g.showIcon})]}):null},$=({message:e,onDelete:s,deletingId:t})=>{let r=e.isInitiator;return(0,a.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=s.getFullYear(),a=s.getMonth()+1,r=s.getDate(),l=s.getHours(),n=s.getMinutes();return`${t}年${a}月${r}日${l}时${n.toString().padStart(2,"0")}分`})(e.timestamp)}),!e.read&&a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),a.jsx("button",{onClick:t=>{t.stopPropagation(),s(e.id)},disabled:t===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:t===e.id?a.jsx("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):a.jsx(d.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"text-sm text-gray-900",children:r?(0,a.jsxs)(a.Fragment,{children:["已与",a.jsx("span",{className:"font-medium",children:e.fromUser.nickname}),"取得联系：",e.contactInfo.value]}):(0,a.jsxs)(a.Fragment,{children:["用户",a.jsx("span",{className:"font-medium",children:e.fromUser.nickname}),"想与您取得联系 ",e.contactInfo.value]})}),a.jsx("button",{onClick:()=>{navigator.clipboard.writeText(e.contactInfo.value)},className:"text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors flex-shrink-0 ml-2",children:"复制"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"text-sm text-gray-700",children:e.postInfo.title}),a.jsx("button",{onClick:()=>{window.open(`/pet-trading-platform/post/detail/?id=${e.postInfo.id}`,"_blank")},className:"text-xs text-blue-600 hover:text-blue-800 transition-colors flex-shrink-0 ml-2",children:"查看帖子"})]})]})]})},z=({message:e,onDelete:s,deletingId:t})=>a.jsx("div",{className:"bg-white border border-gray-200 rounded-lg p-4 mb-3",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"flex-shrink-0 mt-1",children:(e=>{switch(e){case"warning":return a.jsx(S.Z,{className:"w-5 h-5 text-orange-500"});case"error":return a.jsx(S.Z,{className:"w-5 h-5 text-red-500"});default:return a.jsx(M.Z,{className:"w-5 h-5 text-blue-500"})}})(e.severity)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("span",{className:"text-xs text-gray-500",children:(e=>{let s=new Date(e),t=s.getFullYear(),a=s.getMonth()+1,r=s.getDate(),l=s.getHours(),n=s.getMinutes();return`${t}年${a}月${r}日${l}时${n.toString().padStart(2,"0")}分`})(e.timestamp)}),!e.read&&a.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full"})]}),a.jsx("button",{onClick:t=>{t.stopPropagation(),s(e.id)},disabled:t===e.id,className:"p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50",children:t===e.id?a.jsx("div",{className:"w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"}):a.jsx(d.Z,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[a.jsx("p",{className:"text-sm text-gray-900 flex-1",children:"post_reported"===e.notificationType?`您发布的宝贝${e.postTitle||""}被人多次举报已经隐藏，举报理由为：${e.reportReason||"违规内容"}。如要申诉请点击右边的申诉按钮。`:"user_reported"===e.notificationType?"您被人多次举报涉嫌诈骗，如要申诉请点击右边申诉按钮。":e.content}),e.canAppeal&&(0,a.jsxs)("div",{className:"ml-3 flex-shrink-0",children:["none"===e.appealStatus&&a.jsx("button",{onClick:e=>{e.stopPropagation()},className:"text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors",children:"申诉"}),"pending"===e.appealStatus&&a.jsx("span",{className:"text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full",children:"申诉处理中..."}),"approved"===e.appealStatus&&a.jsx("span",{className:"text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full",children:"申诉已通过"}),"rejected"===e.appealStatus&&a.jsx("span",{className:"text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full",children:"申诉已驳回"})]})]})]})]})}),T=()=>{let{user:e,isLoggedIn:s,logout:t,refreshLoginState:i}=(0,x.E)(),[h,p]=(0,r.useState)(!1),[u,f]=(0,r.useState)(!1),[g,y]=(0,r.useState)(!1),j=()=>{p(!0)},w=async()=>{await t(),f(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:"bg-white border-b border-gray-200 sticky top-0 z-40",children:[a.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)(v.default,{href:"/",className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx("span",{className:"text-white font-bold text-lg",children:"\uD83D\uDC3E"})}),a.jsx("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"宠物交易平台"})]}),a.jsx(N,{}),a.jsx("div",{className:"hidden md:flex items-center space-x-3",children:s?(0,a.jsxs)(a.Fragment,{children:[a.jsx(m.Z,{variant:"outline",icon:a.jsx(l.Z,{className:"h-4 w-4"}),onClick:()=>y(!0),children:"通知"}),a.jsx(v.default,{href:"/profile",children:a.jsx(m.Z,{variant:"outline",icon:a.jsx(n.Z,{className:"h-4 w-4"}),children:"我"})}),a.jsx(m.Z,{variant:"outline",icon:a.jsx(c,{className:"h-4 w-4"}),onClick:w,className:"text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300",children:"退出"})]}):a.jsx(m.Z,{onClick:j,children:"登录"})}),a.jsx("div",{className:"md:hidden",children:a.jsx("button",{onClick:()=>f(!u),className:"p-2 rounded-lg hover:bg-gray-100",children:u?a.jsx(d.Z,{className:"h-6 w-6"}):a.jsx(o,{className:"h-6 w-6"})})})]})}),u&&a.jsx("div",{className:"md:hidden border-t border-gray-200 bg-white",children:a.jsx("div",{className:"px-4 py-4 space-y-3",children:s?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"pb-3 border-b border-gray-200",children:(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:e?.nickname}),a.jsx("p",{className:"text-sm text-gray-500",children:e?.email})]})}),(0,a.jsxs)("button",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100 w-full text-left",onClick:()=>{y(!0),f(!1)},children:[a.jsx(l.Z,{className:"h-5 w-5 text-gray-600"}),a.jsx("span",{children:"通知"})]}),(0,a.jsxs)(v.default,{href:"/profile",className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-100",onClick:()=>f(!1),children:[a.jsx(n.Z,{className:"h-5 w-5 text-gray-600"}),a.jsx("span",{children:"我的主页"})]}),(0,a.jsxs)("button",{onClick:w,className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-red-50 w-full text-left",children:[a.jsx(c,{className:"h-5 w-5 text-red-600"}),a.jsx("span",{className:"text-red-600",children:"退出"})]})]}):a.jsx(m.Z,{onClick:()=>{j(),f(!1)},className:"w-full",children:"登录"})})})]}),a.jsx(b,{isOpen:h,onClose:()=>p(!1),onSuccess:async()=>{f(!1),await i()}}),a.jsx(_,{isOpen:g,onClose:()=>y(!1)})]})}},13835:(e,s,t)=>{t.d(s,{Z:()=>d});var a=t(10326);t(17577);var r=t(28676),l=t(99837),n=t(37202),i=t(54659),c=t(18019);let d=({isOpen:e,onClose:s,onConfirm:t,title:d,message:o,confirmText:m="确认",cancelText:x="取消",type:h="info",loading:p=!1,showIcon:u=!0})=>a.jsx(r.u_,{isOpen:e,onClose:s,title:"",size:"sm",showCloseButton:!1,children:a.jsx(r.fe,{children:(0,a.jsxs)("div",{className:"text-center",children:[u&&a.jsx("div",{className:"mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4",children:(()=>{switch(h){case"danger":return a.jsx(n.Z,{className:"w-6 h-6 text-red-600"});case"warning":return a.jsx(n.Z,{className:"w-6 h-6 text-yellow-600"});case"success":return a.jsx(i.Z,{className:"w-6 h-6 text-green-600"});default:return a.jsx(c.Z,{className:"w-6 h-6 text-blue-600"})}})()}),a.jsx("h3",{className:`text-lg font-medium text-gray-900 mb-2 ${u?"":"mt-2"}`,children:d}),a.jsx("p",{className:"text-sm text-gray-500 mb-6",children:o}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-center",children:[a.jsx(l.Z,{variant:"outline",onClick:s,disabled:p,className:"min-w-[80px]",children:x}),a.jsx(l.Z,{variant:(()=>{switch(h){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:t,loading:p,disabled:p,className:"min-w-[80px]",children:m})]})]})})})},89175:(e,s,t)=>{t.d(s,{I:()=>i,g:()=>c});var a=t(10326),r=t(17577),l=t.n(r),n=t(28295);let i=l().forwardRef(({className:e,type:s="text",label:t,error:r,helperText:l,leftIcon:i,rightIcon:c,variant:d="default",...o},m)=>{let x=r?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[t&&a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:t}),(0,a.jsxs)("div",{className:"relative",children:[i&&a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx("span",{className:"text-gray-400",children:i})}),a.jsx("input",{type:s,className:(0,n.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[d],x,i&&"pl-10",c&&"pr-10",e),ref:m,...o}),c&&a.jsx("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:a.jsx("span",{className:"text-gray-400",children:c})})]}),(r||l)&&a.jsx("p",{className:(0,n.cn)("mt-1 text-xs",r?"text-red-600":"text-gray-500"),children:r||l})]})});i.displayName="Input";let c=l().forwardRef(({className:e,label:s,error:t,helperText:r,variant:l="default",...i},c)=>{let d=t?"border-red-500 focus:ring-red-500 focus:border-red-500":"";return(0,a.jsxs)("div",{className:"w-full",children:[s&&a.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s}),a.jsx("textarea",{className:(0,n.cn)("w-full px-3 py-2 text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed resize-none",{default:"border border-gray-300 rounded-md bg-white focus:ring-primary-500 focus:border-primary-500",filled:"border-0 bg-gray-100 rounded-md focus:ring-primary-500 focus:bg-white",outline:"border-2 border-gray-200 rounded-md bg-transparent focus:ring-primary-500 focus:border-primary-500"}[l],d,e),ref:c,...i}),(t||r)&&a.jsx("p",{className:(0,n.cn)("mt-1 text-xs",t?"text-red-600":"text-gray-500"),children:t||r})]})});c.displayName="Textarea"},28676:(e,s,t)=>{t.d(s,{fe:()=>o,u_:()=>d});var a=t(10326),r=t(17577),l=t(60962),n=t(94019),i=t(28295),c=t(99837);let d=({isOpen:e,onClose:s,title:t,children:d,size:o="md",showCloseButton:m=!0,closeOnOverlayClick:x=!0,className:h})=>{if((0,r.useEffect)(()=>{let t=e=>{"Escape"===e.key&&s()};return e&&(document.addEventListener("keydown",t),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",t),document.body.style.overflow="unset"}},[e,s]),!e)return null;let p=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:x?s:void 0}),(0,a.jsxs)("div",{className:(0,i.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[o],h),onClick:e=>e.stopPropagation(),children:[(t||m)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[t&&a.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:t}),m&&a.jsx(c.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:a.jsx(n.Z,{className:"h-5 w-5"})})]}),a.jsx("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,l.createPortal)(p,document.body)},o=({children:e,className:s})=>a.jsx("div",{className:(0,i.cn)("p-4",s),children:e})}};