(()=>{var e={};e.id=931,e.ids=[931,746],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},84770:e=>{"use strict";e.exports=require("crypto")},48858:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(35480),r(16953),r(35866);var s=r(23191),a=r(88716),i=r(37922),l=r.n(i),n=r(95231),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,35480)),"D:\\web-cloudbase-project\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,16953)),"D:\\web-cloudbase-project\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\web-cloudbase-project\\src\\app\\page.tsx"],u="/page",h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},33516:(e,t,r)=>{Promise.resolve().then(r.bind(r,16581))},48998:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},33734:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},17069:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},16581:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>q});var s=r(10326),a=r(17577),i=r(33734),l=r(48998),n=r(67427),o=r(17069),c=r(76557);let d=(0,c.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var u=r(91792),h=r(94019),m=r(98091),g=r(41828),p=r(13570),x=r(28295),y=r(85137);let f=({selectedCategory:e,onCategoryChange:t})=>{let[r,i]=(0,a.useState)([]),[n,o]=(0,a.useState)(!0),[c,u]=(0,a.useState)(!1),[f,b]=(0,a.useState)([]);(0,a.useEffect)(()=>{(async()=>{try{let e=await g.petAPI.getCategories();e.success&&i(e.data)}catch(e){console.error("获取分类失败:",e)}finally{o(!1)}})(),b(p.Fd.getHistory())},[]);let v=(e,r)=>{t(e),e&&r&&(p.Fd.addItem(e,r),b(p.Fd.getHistory()))},j=(e,r)=>{t(e),u(!1),p.Fd.addItem(e,r),b(p.Fd.getHistory())},w=(e,t)=>{t.stopPropagation(),p.Fd.removeItem(e),b(p.Fd.getHistory())},N=()=>{p.Fd.clearHistory(),b([]),u(!1)},k=(0,y.O)(()=>{u(!1)});if(n)return s.jsx("div",{className:"bg-white border-b border-gray-200",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsx("div",{className:"animate-pulse",children:s.jsx("div",{className:"flex space-x-4",children:[1,2,3,4,5,6,7,8,9].map(e=>s.jsx("div",{className:"h-8 bg-gray-200 rounded w-16"},e))})})})});let C=r.filter(e=>1===e.level).sort((e,t)=>e.order-t.order).map(e=>({...e,children:r.filter(t=>2===t.level&&t.parent_id===e.id).sort((e,t)=>e.order-t.order)}));return s.jsx("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsx("div",{className:"md:hidden py-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("select",{value:e,onChange:e=>{let t=r.find(t=>t.id===e.target.value);v(e.target.value,t?.name)},className:"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[s.jsx("option",{value:"",children:"全部分类"}),C.map(e=>(0,s.jsxs)("optgroup",{label:e.name,children:[s.jsx("option",{value:e.id,children:e.name}),e.children.map(t=>(0,s.jsxs)("option",{value:t.id,children:[e.name," > ",t.name]},t.id))]},e.id))]}),f.length>0&&s.jsx("button",{onClick:()=>u(!c),className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600",children:s.jsx(l.Z,{className:"h-4 w-4"})}),c&&f.length>0&&s.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-xs text-gray-500",children:"分类历史"}),s.jsx("button",{onClick:N,className:"text-xs text-gray-400 hover:text-gray-600",children:"清空"})]}),f.map((e,t)=>(0,s.jsxs)("button",{onClick:()=>j(e.value,e.label),className:"w-full px-2 py-1 text-left text-sm hover:bg-gray-50 rounded flex items-center justify-between group",children:[s.jsx("span",{children:e.label}),s.jsx("button",{onClick:t=>w(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600",children:s.jsx(h.Z,{className:"h-3 w-3"})})]},t))]})})]})}),s.jsx("div",{className:"hidden md:block py-4",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 items-center",children:[s.jsx("button",{onClick:()=>v(""),className:(0,x.cn)("px-4 py-2 rounded-lg text-sm font-medium transition-colors",""===e?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"全部分类"}),C.map(t=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("button",{onClick:()=>v(t.id,t.name),className:(0,x.cn)("flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors",e===t.id?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[s.jsx("span",{children:t.name}),t.children.length>0&&s.jsx(d,{className:"h-3 w-3"})]}),t.children.length>0&&s.jsx("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10",children:s.jsx("div",{className:"p-2",children:t.children.map(t=>s.jsx("button",{onClick:()=>v(t.id,t.name),className:(0,x.cn)("block w-full text-left px-3 py-2 text-sm rounded-md transition-colors",e===t.id?"bg-primary-50 text-primary-700":"text-gray-700 hover:bg-gray-100"),children:t.name},t.id))})})]},t.id)),f.length>0&&(0,s.jsxs)("div",{className:"relative",ref:k,children:[(0,s.jsxs)("button",{onClick:()=>u(!c),className:(0,x.cn)("px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1",c?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[s.jsx(l.Z,{className:"h-3 w-3"}),s.jsx("span",{children:"历史"})]}),c&&s.jsx("div",{className:"absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-xs text-gray-500 font-medium",children:"分类历史"}),(0,s.jsxs)("button",{onClick:N,className:"text-xs text-gray-400 hover:text-gray-600 flex items-center space-x-1",children:[s.jsx(m.Z,{className:"h-3 w-3"}),s.jsx("span",{children:"清空"})]})]}),f.map((e,t)=>(0,s.jsxs)("button",{onClick:()=>j(e.value,e.label),className:"w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm rounded-md group flex items-center justify-between",children:[s.jsx("span",{className:"text-gray-700",children:e.label}),s.jsx("button",{onClick:t=>w(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity p-1",children:s.jsx(h.Z,{className:"h-3 w-3"})})]},t))]})})]})]})})]})})};var b=r(42443),v=r(22502),j=r(16545);let w=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))});var N=r(39258);function k({positionId:e,className:t="",fallbackContent:r}){let[i,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(null),[c,d]=(0,a.useState)(0),[u,h]=(0,a.useState)(!0),[m,g]=(0,a.useState)(!1),[p,x]=(0,a.useState)(!0),y=e=>{f(e._id),e.target_url&&window.open(e.target_url,"_blank")},f=async e=>{try{console.log("广告点击记录:",{ad_id:e,timestamp:new Date})}catch(e){console.error("记录广告点击失败:",e)}},b=()=>{g(!0),localStorage.setItem(`ad_hidden_${e}`,Date.now().toString())};if(p)return s.jsx("div",{className:`animate-pulse ${t}`,children:s.jsx("div",{className:"bg-gray-200 rounded-lg h-20"})});if(!(()=>{if(!n||"active"!==n.status||!i||0===i.length||m)return!1;let t=localStorage.getItem(`ad_hidden_${e}`);if(t){let e=parseInt(t);if((Date.now()-e)/36e5<24)return!1}return!0})())return r?s.jsx("div",{className:t,children:r}):null;let v=i[c];return s.jsx("div",{className:t,children:(()=>{switch(n?.ad_type){case"banner":return(0,s.jsxs)("div",{className:"relative bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden",children:[s.jsx("button",{onClick:b,className:"absolute top-2 right-2 z-10 bg-white/80 hover:bg-white rounded-full p-1 transition-colors",title:"隐藏广告",children:s.jsx(w,{className:"h-4 w-4 text-gray-600"})}),(0,s.jsxs)("div",{className:"flex items-center p-4 cursor-pointer hover:bg-black/5 transition-colors",onClick:()=>y(v),children:[v.image_url&&s.jsx("img",{src:v.image_url,alt:v.title,className:"w-16 h-16 rounded-lg object-cover mr-4"}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:v.title}),s.jsx("p",{className:"text-sm text-gray-600 line-clamp-2",children:v.content})]}),s.jsx("div",{className:"ml-4",children:s.jsx("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:"广告"})})]})]});case"feed":return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-gray-100",children:[s.jsx("span",{className:"text-xs text-gray-500 bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:"推广内容"}),s.jsx("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 transition-colors",title:"隐藏此广告",children:s.jsx(N.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>y(v),children:[v.image_url&&s.jsx("img",{src:v.image_url,alt:v.title,className:"w-full h-40 object-cover rounded-lg mb-3"}),s.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:v.title}),s.jsx("p",{className:"text-sm text-gray-600 mb-3 line-clamp-3",children:v.content}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"text-xs text-gray-500",children:"点击了解更多"}),s.jsx("div",{className:"flex items-center space-x-2",children:i.length>1&&s.jsx("div",{className:"flex space-x-1",children:i.map((e,t)=>s.jsx("div",{className:`w-2 h-2 rounded-full ${t===c?"bg-blue-500":"bg-gray-300"}`},t))})})]})]})]});case"popup":return s.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-sm w-full overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative",children:[s.jsx("button",{onClick:b,className:"absolute top-2 right-2 z-10 bg-black/20 hover:bg-black/40 text-white rounded-full p-1 transition-colors",children:s.jsx(w,{className:"h-5 w-5"})}),v.image_url&&s.jsx("img",{src:v.image_url,alt:v.title,className:"w-full h-48 object-cover"})]}),(0,s.jsxs)("div",{className:"p-4",children:[s.jsx("h3",{className:"font-bold text-lg text-gray-900 mb-2",children:v.title}),s.jsx("p",{className:"text-gray-600 mb-4",children:v.content}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>y(v),className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors",children:"了解更多"}),s.jsx("button",{onClick:b,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"跳过"})]})]})]})});default:return null}})()})}var C=r(20603),S=r(89453),I=r(12670);class T{static getInstance(){return T.instance||(T.instance=new T),T.instance}async optimizedPostQuery(e){let t=Date.now(),s=`posts_${JSON.stringify(e)}`;if(!1!==e.useCache){let e=I.dataCache.get(s);if(e)return this.recordQueryStats("optimizedPostQuery",Date.now()-t),e}try{let a=this.buildOptimizedQuery(e),{petAPI:i}=await Promise.resolve().then(r.bind(r,41828)),l=await i.getOptimizedPosts(a);return l.success&&!1!==e.useCache&&I.dataCache.set(s,l,12e4),this.recordQueryStats("optimizedPostQuery",Date.now()-t),l}catch(e){throw console.error("查询优化失败:",e),e}}buildOptimizedQuery(e){let t={action:"query",page:e.page||1,limit:Math.min(e.limit||10,20),includeUserInfo:e.includeUserInfo||!1,includeCategoryInfo:e.includeCategoryInfo||!1};return e.category&&(t.category=e.category),e.type&&(t.type=e.type),e.location&&(t.location=e.location),e.userId&&(t.userId=e.userId),e.sortBy&&(t.sortBy=e.sortBy),e.currentUserId&&(t.currentUserId=e.currentUserId),t}async batchQuery(e,t=5){let r=[];for(let s=0;s<e.length;s+=t){let a=e.slice(s,s+t);(await Promise.allSettled(a.map(e=>e()))).forEach(e=>{"fulfilled"===e.status?r.push(e.value):console.error("批量查询失败:",e.reason)})}return r}async preloadRelatedData(e){if(!e.length)return;let t=Array.from(new Set(e.map(e=>e.user_id||e.author_id).filter(Boolean))),r=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));t.length>0&&this.preloadUsers(t),r.length>0&&this.preloadCategories(r)}async preloadUsers(e){e.filter(e=>!I.dataCache.get(`user_${e}`)).length}async preloadCategories(e){e.filter(e=>!I.dataCache.get(`category_${e}`)).length}recordQueryStats(e,t){let r=this.queryStats.get(e)||{count:0,avgTime:0};r.count++,r.avgTime=(r.avgTime*(r.count-1)+t)/r.count,this.queryStats.set(e,r)}getQueryStats(){return new Map(this.queryStats)}clearStats(){this.queryStats.clear()}constructor(){this.queryStats=new Map}}let _=T.getInstance();var P=r(74131);class E{generateKey(e,t){let{limit:r,...s}=e;return JSON.stringify({...s,page:t,limit:r})}get(e,t){let r=this.generateKey(e,t),s=this.cache.get(r);return s&&Date.now()-s.timestamp<this.TTL?s:null}set(e,t,r,s,a){let i=this.generateKey(e,t);this.cache.set(i,{data:r,timestamp:Date.now(),hasMore:s,totalCount:a})}clear(){this.cache.clear(),console.log("帖子缓存已清理")}clearByFilters(e){let t=[];this.cache.forEach((r,s)=>{try{let r=JSON.parse(s),a=!1;if(0===Object.keys(e).length)a=!0;else for(let[t,s]of Object.entries(e))if(r[t]===s){a=!0;break}a&&t.push(s)}catch(e){}}),t.forEach(e=>this.cache.delete(e)),console.log(`清理了 ${t.length} 个缓存项`)}async preloadNextPage(e,t,r){let s=t+1,a=this.generateKey(e,s);if(!this.cache.get(a))try{let t=await _.optimizedPostQuery({page:s,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0,currentUserId:r});if(t.success&&t.data){let r=Array.isArray(t.data)?t.data:t.data.posts||[],a=t.pagination?.hasMore||!1,i=t.pagination?.total||r.length;this.set(e,s,r,a,i),console.log(`预加载第${s}页成功，共${r.length}条数据`)}}catch(e){console.warn("预加载下一页失败:",e)}}constructor(){this.cache=new Map,this.TTL=3e4}}let L=new E,D=({filters:e,enabled:t=!0,debounceMs:r=300})=>{let{user:s}=(0,P.a)(),[i,l]=(0,a.useState)([]),[n,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[u,h]=(0,a.useState)(null),[m,g]=(0,a.useState)(!0),[p,x]=(0,a.useState)(0),y=(0,a.useRef)(null),f=(0,a.useRef)(null),b=(0,a.useRef)(e),v=(0,a.useCallback)((e,t)=>{if(!t.trim())return e;let r=t.toLowerCase().trim();return e.filter(e=>!!(e.breed&&e.breed.toLowerCase().includes(r)||e.title&&e.title.toLowerCase().includes(r)||e.tags&&e.tags.some(e=>e.toLowerCase().includes(r))))},[]),j=(0,a.useCallback)(async(e,t=!1,r)=>{try{let a;let i=t?e.page:1;if(!t){let t=L.get(e,i);if(t){l(t.data),g(t.hasMore),x(t.totalCount),h(null);return}}t?d(!0):o(!0);let n=Date.now();a=await _.optimizedPostQuery({page:i,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,type:"all"!==e.petType?e.petType:void 0,location:e.location||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0,currentUserId:s?._id});let c=Date.now()-n;if(void 0!==S.Bm&&S.Bm.recordApiResponse("optimizedPostQuery",c),r?.aborted)return;if(a.success){let r=a.data||[];e.breed&&(r=v(r,e.breed)),t?l(e=>[...e,...r]):l(r);let n=a.pagination?.hasMore||!1,o=a.pagination?.total||r.length;g(n),x(o),h(null),!t&&(L.set(e,i,r,n,o),n&&1===i&&setTimeout(()=>{L.preloadNextPage(e,i,s?._id)},1e3))}else throw Error(a.message||"获取数据失败")}catch(e){if(r?.aborted)return;console.error("获取宠物列表失败:",e),h(e.message||"获取数据失败"),t||C.C.error("获取宠物列表失败，请重试")}finally{r?.aborted||(o(!1),d(!1))}},[v]),w=(0,a.useCallback)((e,t=!1)=>{if(y.current&&y.current.abort(),f.current&&clearTimeout(f.current),t){let t=new AbortController;y.current=t,j(e,!0,t.signal);return}f.current=setTimeout(()=>{let t=new AbortController;y.current=t,j(e,!1,t.signal)},r)},[j,r]),N=(0,a.useCallback)(()=>{if(!c&&m&&t){let e={...b.current,page:b.current.page+1};b.current=e,w(e,!0)}},[c,m,t,w]),k=(0,a.useCallback)(()=>{if(L.clear(),l([]),h(null),g(!0),x(0),t){let t={...e,page:1};b.current=t,w(t)}},[e,t,w]);return(0,a.useEffect)(()=>{if(t)return(b.current.category!==e.category||b.current.petType!==e.petType||b.current.location!==e.location||b.current.breed!==e.breed||b.current.sortBy!==e.sortBy||1===e.page)&&(l([]),h(null),g(!0),x(0)),b.current=e,w(e),()=>{f.current&&clearTimeout(f.current)}},[e,t,w]),(0,a.useEffect)(()=>()=>{y.current&&y.current.abort(),f.current&&clearTimeout(f.current)},[]),{posts:i,loading:n,loadingMore:c,error:u,hasMore:m,loadMore:N,refresh:k,totalCount:p}},O=({filters:e})=>{let{posts:t,loading:r,loadingMore:i,error:l,hasMore:n,loadMore:o,refresh:c,totalCount:d}=D({filters:e}),{ref:u,inView:h}=(0,b.YD)({threshold:0,rootMargin:"100px"});return((0,a.useEffect)(()=>{h&&n&&!r&&!i&&o()},[h,n,r,i,o]),l&&0===t.length)?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("div",{className:"text-gray-500 mb-4",children:[s.jsx("p",{className:"text-lg font-medium",children:"加载失败"}),s.jsx("p",{className:"text-sm",children:l})]}),s.jsx("button",{onClick:()=>{c()},className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"重试"})]}):r&&0===t.length?s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:Array.from({length:20}).map((e,t)=>s.jsx(j.gG,{},t))}):r||0!==t.length?(0,s.jsxs)("div",{className:"space-y-6",children:[s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:(()=>{let e=[];for(let r=0;r<t.length;r++)e.push(s.jsx(v.Z,{post:t[r]},t[r]._id)),(r+1)%6==0&&r<t.length-1&&e.push(s.jsx(k,{positionId:"home_feed",className:"col-span-2 md:col-span-1"},`ad-${r}`));return e})()}),n&&s.jsx("div",{ref:u,className:"flex justify-center py-8",children:i?s.jsx(j.gb,{size:"sm",text:"加载更多..."}):s.jsx("div",{className:"text-gray-400 text-sm",children:"滑动到底部加载更多"})}),!n&&t.length>0&&s.jsx("div",{className:"text-center py-8 text-gray-400 text-sm",children:"已显示全部内容"})]}):s.jsx("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[s.jsx("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),s.jsx("p",{className:"text-lg font-medium mb-2",children:"暂无宠物信息"}),s.jsx("p",{className:"text-sm",children:e.category?"该分类下暂无宠物，试试其他分类吧":"还没有人发布宠物，快来成为第一个吧！"})]})})};var B=r(99837),M=r(77636);let R="location_filter_history",z=({selectedLocation:e,onLocationChange:t})=>{let[r,i]=(0,a.useState)(e),[l,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(!1),d=()=>{try{let e=localStorage.getItem(R);return e?JSON.parse(e):[]}catch{return[]}},u=e=>{try{localStorage.setItem(R,JSON.stringify(e))}catch{}},h=e=>{if(!e.trim())return;let t=[e,...d().filter(t=>t!==e)].slice(0,10);n(t),u(t)};(0,a.useEffect)(()=>{n(d())},[]);let m=e=>{i(e),t(e),c(!1)},g=()=>{r&&r.trim()&&h(r.trim())},p=(0,y.O)(()=>{c(!1)});return(0,a.useEffect)(()=>{i(e)},[e]),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(M.Z,{className:"h-4 w-4 text-gray-400"})}),s.jsx("input",{type:"text",value:r,onChange:e=>{let r=e.target.value;i(r),t(r)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),g())},onBlur:g,placeholder:"输入地区筛选，如：北京、上海等",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})]}),(0,s.jsxs)("div",{className:"relative",ref:p,children:[s.jsx("button",{type:"button",onClick:()=>{c(!o)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看地区历史",children:s.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),o&&l.length>0&&(0,s.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,s.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[s.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"地区历史"]}),s.jsx("button",{onClick:()=>{n([]),u([]),c(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),s.jsx("div",{className:"max-h-48 overflow-y-auto",children:l.map((e,t)=>(0,s.jsxs)("div",{onClick:()=>m(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-center",children:[s.jsx(M.Z,{className:"h-3 w-3 text-gray-400 mr-2 flex-shrink-0"}),s.jsx("span",{className:"text-sm",children:e})]},`${e}-${t}`))})]})]})]}),r&&s.jsx("div",{className:"flex justify-end",children:s.jsx("button",{onClick:()=>{i(""),t(""),c(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})},U="breed_filter_history",Z=({selectedBreed:e,onBreedChange:t})=>{let[r,i]=(0,a.useState)(e),[l,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(!1),d=()=>{try{let e=localStorage.getItem(U);return e?JSON.parse(e):[]}catch{return[]}},u=e=>{try{localStorage.setItem(U,JSON.stringify(e))}catch{}},h=e=>{if(!e.trim())return;let t=[e,...d().filter(t=>t!==e)].slice(0,10);n(t),u(t)};(0,a.useEffect)(()=>{n(d())},[]);let m=e=>{i(e),t(e),c(!1)},g=()=>{r&&r.trim()&&h(r.trim())},p=(0,y.O)(()=>{c(!1)});return(0,a.useEffect)(()=>{i(e)},[e]),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[s.jsx("div",{className:"flex-1",children:s.jsx("input",{type:"text",value:r,onChange:e=>{let r=e.target.value;i(r),t(r)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),g())},onBlur:g,placeholder:"输入品种筛选，如：柴犬、英短等",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})}),(0,s.jsxs)("div",{className:"relative",ref:p,children:[s.jsx("button",{type:"button",onClick:()=>{c(!o)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看品种历史",children:s.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),o&&l.length>0&&(0,s.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,s.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[s.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"品种历史"]}),s.jsx("button",{onClick:()=>{n([]),u([]),c(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),s.jsx("div",{className:"max-h-48 overflow-y-auto",children:l.map((e,t)=>s.jsx("div",{onClick:()=>m(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors",children:s.jsx("span",{className:"text-sm",children:e})},`${e}-${t}`))})]})]})]}),r&&s.jsx("div",{className:"flex justify-end",children:s.jsx("button",{onClick:()=>{i(""),t(""),c(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})},H=(0,c.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),A=({filters:e,onRemoveFilter:t,onClearAll:r,hasActiveFilters:a,totalCount:i})=>{if(!a)return null;let l=(e,t)=>{switch(e){case"category":return`分类: ${t}`;case"petType":return`类型: ${({breeding:"配种",selling:"出售",lost:"寻回"})[t]||t}`;case"location":return`地区: ${t}`;case"breed":return`品种: ${t}`;case"sortBy":return`排序: ${({created_at:"最新发布",likes_count:"最多点赞",wants_count:"最想要",avg_rating:"最高评分",priority:"智能推荐"})[t]||t}`;default:return`${e}: ${t}`}},n=Object.entries(e).filter(([e,t])=>"page"!==e&&"limit"!==e&&("petType"!==e||"all"!==t)&&("sortBy"!==e||"priority"!==t)&&t&&""!==t);return s.jsx("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-wrap",children:[s.jsx("span",{className:"text-sm text-gray-600 font-medium",children:"筛选条件:"}),n.map(([e,r])=>(0,s.jsxs)("div",{className:"inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full",children:[s.jsx("span",{children:l(e,r)}),s.jsx("button",{onClick:()=>t(e),className:"ml-1.5 inline-flex items-center justify-center w-3 h-3 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full transition-colors","aria-label":`移除${e}筛选条件`,children:s.jsx(h.Z,{className:"w-2 h-2"})})]},e)),void 0!==i&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["共找到 ",i," 个结果"]})]}),(0,s.jsxs)("button",{onClick:r,className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors",children:[s.jsx(H,{className:"w-4 h-4 mr-1"}),"清除全部"]})]})})};var $=r(35047);let K={category:"",petType:"all",location:"",breed:"",sortBy:"priority",page:1,limit:20},F=()=>{let e=(0,$.useRouter)(),t=(0,$.useSearchParams)(),r=(0,a.useCallback)(()=>({category:t.get("category")||K.category,petType:t.get("petType")||K.petType,location:t.get("location")||K.location,breed:t.get("breed")||K.breed,sortBy:t.get("sortBy")||K.sortBy,page:parseInt(t.get("page")||"1"),limit:parseInt(t.get("limit")||"20")}),[t]),[s,i]=(0,a.useState)(r),l=(0,a.useCallback)(t=>{let r=new URLSearchParams;t.category&&t.category!==K.category&&r.set("category",t.category),t.petType!==K.petType&&r.set("petType",t.petType),t.location&&t.location!==K.location&&r.set("location",t.location),t.breed&&t.breed!==K.breed&&r.set("breed",t.breed),t.sortBy!==K.sortBy&&r.set("sortBy",t.sortBy),t.page!==K.page&&r.set("page",t.page.toString());let s=r.toString()?`?${r.toString()}`:"/";e.replace(s,{scroll:!1})},[e]),n=(0,a.useCallback)((e,t)=>{i(r=>{let s={...r,[e]:t,page:"page"===e?t:1};return l(s),s})},[l]),o=(0,a.useCallback)(()=>{i(K),l(K)},[l]),c=(0,a.useCallback)(()=>{n("page",1)},[n]),d=(0,a.useCallback)(()=>{let e=new URLSearchParams;return Object.entries(s).forEach(([t,r])=>{r&&r!==K[t]&&e.set(t,r.toString())}),e},[s]),u=(0,a.useCallback)(()=>Object.entries(s).some(([e,t])=>"page"!==e&&"limit"!==e&&t!==K[e]),[s]);return(0,a.useEffect)(()=>{i(r())},[t,r]),{filters:s,updateFilter:n,resetFilters:o,resetPage:c,getQueryParams:d,hasActiveFilters:u()}},q=()=>{let{filters:e,updateFilter:t,resetFilters:r,hasActiveFilters:c}=F(),[h,m]=(0,a.useState)(!1),[g,p]=(0,a.useState)(!1),y=[{value:"priority",label:"智能推荐",icon:i.Z},{value:"created_at",label:"最新发布",icon:l.Z},{value:"likes_count",label:"最多点赞",icon:n.Z},{value:"wants_count",label:"最想要",icon:o.Z},{value:"avg_rating",label:"最高评分",icon:i.Z}],b=[{value:"all",label:"全部",color:"bg-gray-500"},{value:"breeding",label:"配种",color:"bg-pink-500"},{value:"selling",label:"出售",color:"bg-green-500"},{value:"lost",label:"寻回",color:"bg-orange-500"}],v=y.find(t=>t.value===e.sortBy),j=b.find(t=>t.value===e.petType);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(u.Z,{}),s.jsx(f,{selectedCategory:e.category,onCategoryChange:e=>t("category",e)}),s.jsx(A,{filters:e,onRemoveFilter:e=>{switch(e){case"category":t("category","");break;case"petType":t("petType","all");break;case"location":t("location","");break;case"breed":t("breed","");break;case"sortBy":t("sortBy","priority")}},onClearAll:r,hasActiveFilters:c}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[s.jsx("div",{className:"flex items-center justify-between",children:s.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"发现宠物"})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(B.Z,{variant:"outline",onClick:()=>m(!h),className:"flex items-center space-x-2",children:[v&&s.jsx(v.icon,{className:"w-4 h-4"}),s.jsx("span",{children:v?.label||"排序"}),s.jsx(d,{className:"w-4 h-4"})]}),h&&s.jsx("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:s.jsx("div",{className:"py-1",children:y.map(r=>(0,s.jsxs)("button",{onClick:()=>{t("sortBy",r.value),m(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.sortBy===r.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[s.jsx(r.icon,{className:"w-4 h-4"}),s.jsx("span",{children:r.label})]},r.value))})})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(B.Z,{variant:"outline",onClick:()=>p(!g),className:"flex items-center space-x-2",children:[s.jsx("div",{className:(0,x.cn)("w-3 h-3 rounded-full",j?.color)}),s.jsx("span",{children:j?.label||"类型"}),s.jsx(d,{className:"w-4 h-4"})]}),g&&s.jsx("div",{className:"absolute top-full left-0 mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:s.jsx("div",{className:"py-1",children:b.map(r=>(0,s.jsxs)("button",{onClick:()=>{t("petType",r.value),p(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.petType===r.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[s.jsx("div",{className:(0,x.cn)("w-3 h-3 rounded-full",r.color)}),s.jsx("span",{children:r.label})]},r.value))})})]}),s.jsx("div",{className:"flex-1 min-w-[200px]",children:s.jsx(z,{selectedLocation:e.location,onLocationChange:e=>t("location",e)})}),s.jsx("div",{className:"flex-1 min-w-[200px]",children:s.jsx(Z,{selectedBreed:e.breed,onBreedChange:e=>t("breed",e)})})]})]}),s.jsx(k,{positionId:"home_banner",className:"mb-6",fallbackContent:s.jsx("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 text-center",children:s.jsx("p",{className:"text-gray-600",children:"\uD83D\uDC3E 发现更多可爱的宠物朋友"})})}),s.jsx(O,{filters:e})]})]})}},12670:(e,t,r)=>{"use strict";r.d(t,{U_:()=>i,dataCache:()=>l});class s{constructor(e={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...e}}set(e,t,r){let s=Date.now(),a=r||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(e,{data:t,timestamp:s,ttl:a,accessCount:0,lastAccess:s})}get(e){let t=this.cache.get(e);if(!t)return null;let r=Date.now();return r-t.timestamp>t.ttl?(this.cache.delete(e),null):(t.accessCount++,t.lastAccess=r,t.data)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}evict(){let e;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":e=this.findLRUKey();break;case"FIFO":e=this.findFIFOKey();break;case"TTL":e=this.findExpiredKey();break;default:e=this.cache.keys().next().value}e&&this.cache.delete(e)}}findLRUKey(){let e;let t=Date.now();return this.cache.forEach((r,s)=>{r.lastAccess<t&&(t=r.lastAccess,e=s)}),e}findFIFOKey(){let e;let t=Date.now();return this.cache.forEach((r,s)=>{r.timestamp<t&&(t=r.timestamp,e=s)}),e}findExpiredKey(){let e;let t=Date.now();return(this.cache.forEach((r,s)=>{if(t-r.timestamp>r.ttl){e=s;return}}),e)?e:this.findLRUKey()}getStats(){let e=Date.now(),t=0,r=0;return this.cache.forEach(s=>{e-s.timestamp>s.ttl&&t++,r+=JSON.stringify(s.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:t,totalSize:r,hitRate:this.calculateHitRate()}}calculateHitRate(){let e=0;return this.cache.forEach(t=>{e+=t.accessCount}),e>0?this.cache.size/e*100:0}}class a{constructor(e="pet_cache_",t=!1){this.prefix=e,this.storage=null}set(e,t,r=864e5){if(!this.storage)return;let s={data:t,timestamp:Date.now(),ttl:r};try{this.storage.setItem(this.prefix+e,JSON.stringify(s))}catch(t){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+e,JSON.stringify(s))}catch(e){console.error("缓存设置失败:",e)}}}get(e){if(!this.storage)return null;try{let t=this.storage.getItem(this.prefix+e);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.timestamp>r.ttl)return this.storage.removeItem(this.prefix+e),null;return r.data}catch(e){return console.error("缓存读取失败:",e),null}}delete(e){this.storage&&this.storage.removeItem(this.prefix+e)}cleanup(){if(!this.storage)return;let e=Date.now(),t=[];for(let r=0;r<this.storage.length;r++){let s=this.storage.key(r);if(s&&s.startsWith(this.prefix))try{let r=this.storage.getItem(s);if(r){let a=JSON.parse(r);e-a.timestamp>a.ttl&&t.push(s)}}catch(e){t.push(s)}}t.forEach(e=>this.storage.removeItem(e))}clear(){if(!this.storage)return;let e=[];for(let t=0;t<this.storage.length;t++){let r=this.storage.key(t);r&&r.startsWith(this.prefix)&&e.push(r)}e.forEach(e=>this.storage.removeItem(e))}}let i=new s({ttl:18e5,maxSize:50,strategy:"LRU"}),l=new s({ttl:3e5,maxSize:100,strategy:"LRU"});new a("pet_app_")},13570:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>i,KX:()=>l});class s{constructor(e,t=10){this.storageKey=e,this.maxItems=t}getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e).sort((e,t)=>t.timestamp-e.timestamp)}catch(e){console.error("获取历史记录失败:",e)}return[]}addItem(e,t){if(!e.trim())return;let r={value:e.trim(),label:t||e.trim(),timestamp:Date.now()},s=this.getHistory();(s=s.filter(e=>e.value!==r.value)).unshift(r),s.length>this.maxItems&&(s=s.slice(0,this.maxItems)),this.saveHistory(s)}removeItem(e){let t=this.getHistory().filter(t=>t.value!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getHistoryValues(){return this.getHistory().map(e=>e.value)}hasItem(e){return this.getHistoryValues().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存历史记录失败:",e)}}}class a{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e){let t=JSON.parse(e),r=Date.now()-864e5*this.maxDays,s=t.filter(e=>e.timestamp>r);return s.length!==t.length&&this.saveHistory(s),s.sort((e,t)=>t.timestamp-e.timestamp)}}catch(e){console.error("获取浏览历史记录失败:",e)}return[]}addBrowseRecord(e,t,r,s,a){if(!e||!t)return;let i={postId:e,title:t.trim(),author:r.trim(),authorId:s,image:a,timestamp:Date.now()},l=this.getHistory();(l=l.filter(e=>e.postId!==i.postId)).unshift(i),l.length>this.maxItems&&(l=l.slice(0,this.maxItems)),this.saveHistory(l)}removeRecord(e){let t=this.getHistory().filter(t=>t.postId!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getRecentPostIds(){return this.getHistory().map(e=>e.postId)}hasBrowsed(e){return this.getRecentPostIds().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存浏览历史记录失败:",e)}}constructor(){this.storageKey="browse_history",this.maxDays=3,this.maxItems=50}}new s("location_history",10);let i=new s("category_history",8),l=new a},89453:(e,t,r)=>{"use strict";r.d(t,{Bm:()=>a});class s{constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}static getInstance(){return s.instance||(s.instance=new s),s.instance}initializeObservers(){}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),r=t[t.length-1];this.metrics.largestContentfulPaint=r.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.hadRecentInput||(e+=t.value,this.metrics.cumulativeLayoutShift=e)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let t=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(t)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,t){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(t)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((t,r)=>{let s=t.reduce((e,t)=>e+t,0)/t.length;e[r]={avgResponseTime:Math.round(s),callCount:t.length,maxResponseTime:Math.round(Math.max(...t)),minResponseTime:Math.round(Math.min(...t))}});let t=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,t)=>e+t,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(t)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,t=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,r=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+t+r)/3),breakdown:{loading:e,interactivity:t,visualStability:r}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}}let a=s.getInstance()},35480:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`D:\web-cloudbase-project\src\app\page.tsx#default`)},39258:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(17577);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,201,240,535,792],()=>r(48858));module.exports=s})();