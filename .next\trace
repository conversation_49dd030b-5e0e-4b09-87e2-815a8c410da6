[{"name": "generate-buildid", "duration": 107, "timestamp": 43281905884, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753816088009, "traceId": "285185c5183d9357"}, {"name": "load-custom-routes", "duration": 384, "timestamp": 43281906343, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753816088009, "traceId": "285185c5183d9357"}, {"name": "create-pages-mapping", "duration": 212, "timestamp": 43282007899, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753816088111, "traceId": "285185c5183d9357"}, {"name": "collect-app-paths", "duration": 3027, "timestamp": 43282008146, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753816088111, "traceId": "285185c5183d9357"}, {"name": "create-app-mapping", "duration": 1091, "timestamp": 43282011284, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753816088114, "traceId": "285185c5183d9357"}, {"name": "public-dir-conflict-check", "duration": 696, "timestamp": 43282013093, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753816088116, "traceId": "285185c5183d9357"}, {"name": "generate-routes-manifest", "duration": 4117, "timestamp": 43282014049, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753816088117, "traceId": "285185c5183d9357"}, {"name": "create-dist-dir", "duration": 416, "timestamp": 43282019776, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753816088122, "traceId": "285185c5183d9357"}, {"name": "write-routes-manifest", "duration": 43616, "timestamp": 43282108131, "id": 12, "parentId": 1, "tags": {}, "startTime": 1753816088211, "traceId": "285185c5183d9357"}, {"name": "generate-required-server-files", "duration": 1831, "timestamp": 43282151927, "id": 13, "parentId": 1, "tags": {}, "startTime": 1753816088255, "traceId": "285185c5183d9357"}, {"name": "create-entrypoints", "duration": 85913, "timestamp": 43283959464, "id": 17, "parentId": 15, "tags": {}, "startTime": 1753816090062, "traceId": "285185c5183d9357"}, {"name": "generate-webpack-config", "duration": 818841, "timestamp": 43284045632, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753816090148, "traceId": "285185c5183d9357"}, {"name": "next-trace-entrypoint-plugin", "duration": 2964, "timestamp": 43285048961, "id": 20, "parentId": 19, "tags": {}, "startTime": 1753816091152, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 676789, "timestamp": 43285064797, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 697233, "timestamp": 43285064846, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 702630, "timestamp": 43285065262, "id": 36, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1013076, "timestamp": 43285064184, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012422, "timestamp": 43285064875, "id": 25, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Factivities%2Fpage&name=app%2Factivities%2Fpage&pagePath=private-next-app-dir%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012392, "timestamp": 43285064916, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdebug%2Fpage&name=app%2Fdebug%2Fpage&pagePath=private-next-app-dir%2Fdebug%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fdebug%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012506, "timestamp": 43285064931, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffavorites%2Fpage&name=app%2Ffavorites%2Fpage&pagePath=private-next-app-dir%2Ffavorites%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ffavorites%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012501, "timestamp": 43285064963, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012530, "timestamp": 43285064948, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fmessages%2Fpage&name=app%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fmessages%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012256, "timestamp": 43285065237, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012318, "timestamp": 43285065181, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpost%2Fdetail%2Fpage&name=app%2Fpost%2Fdetail%2Fpage&pagePath=private-next-app-dir%2Fpost%2Fdetail%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpost%2Fdetail%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012255, "timestamp": 43285065248, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftest-email%2Fpage&name=app%2Ftest-email%2Fpage&pagePath=private-next-app-dir%2Ftest-email%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ftest-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012254, "timestamp": 43285065253, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fupload%2Fpage&name=app%2Fupload%2Fpage&pagePath=private-next-app-dir%2Fupload%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fupload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012247, "timestamp": 43285065265, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factivities%2Fpage&name=app%2Fadmin%2Factivities%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012246, "timestamp": 43285065271, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fads%2Fpage&name=app%2Fadmin%2Fads%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fads%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fads%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012244, "timestamp": 43285065277, "id": 39, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fdashboard%2Fpage&name=app%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012245, "timestamp": 43285065281, "id": 40, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012244, "timestamp": 43285065285, "id": 41, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fperformance%2Fpage&name=app%2Fadmin%2Fperformance%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fperformance%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fperformance%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012249, "timestamp": 43285065290, "id": 42, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fposts%2Fpage&name=app%2Fadmin%2Fposts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fposts%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fposts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1012252, "timestamp": 43285065295, "id": 43, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1013286, "timestamp": 43285065257, "id": 35, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2F%5Bid%5D%2Fpage&name=app%2Fprofile%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprofile%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091168, "traceId": "285185c5183d9357"}, {"name": "add-entry", "duration": 1298590, "timestamp": 43285064897, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fimage%2Froute&name=app%2Fapi%2Fimage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fimage%2Froute.ts&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fapi%2Fimage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753816091167, "traceId": "285185c5183d9357"}, {"name": "make", "duration": 2611145, "timestamp": 43285063625, "id": 21, "parentId": 19, "tags": {}, "startTime": 1753816091166, "traceId": "285185c5183d9357"}, {"name": "get-entries", "duration": 7904, "timestamp": 43287676502, "id": 147, "parentId": 146, "tags": {}, "startTime": 1753816093779, "traceId": "285185c5183d9357"}, {"name": "node-file-trace-plugin", "duration": 105722, "timestamp": 43287689511, "id": 148, "parentId": 146, "tags": {"traceEntryCount": "40"}, "startTime": 1753816093792, "traceId": "285185c5183d9357"}, {"name": "collect-traced-files", "duration": 905, "timestamp": 43287795249, "id": 149, "parentId": 146, "tags": {}, "startTime": 1753816093898, "traceId": "285185c5183d9357"}, {"name": "finish-modules", "duration": 119917, "timestamp": 43287676243, "id": 146, "parentId": 20, "tags": {}, "startTime": 1753816093779, "traceId": "285185c5183d9357"}, {"name": "chunk-graph", "duration": 46831, "timestamp": 43287921854, "id": 151, "parentId": 150, "tags": {}, "startTime": 1753816094024, "traceId": "285185c5183d9357"}, {"name": "optimize-modules", "duration": 45, "timestamp": 43287968851, "id": 153, "parentId": 150, "tags": {}, "startTime": 1753816094071, "traceId": "285185c5183d9357"}, {"name": "optimize-chunks", "duration": 89646, "timestamp": 43287969041, "id": 154, "parentId": 150, "tags": {}, "startTime": 1753816094072, "traceId": "285185c5183d9357"}, {"name": "optimize-tree", "duration": 176, "timestamp": 43288058803, "id": 155, "parentId": 150, "tags": {}, "startTime": 1753816094161, "traceId": "285185c5183d9357"}, {"name": "optimize-chunk-modules", "duration": 71365, "timestamp": 43288059096, "id": 156, "parentId": 150, "tags": {}, "startTime": 1753816094162, "traceId": "285185c5183d9357"}, {"name": "optimize", "duration": 161842, "timestamp": 43287968778, "id": 152, "parentId": 150, "tags": {}, "startTime": 1753816094071, "traceId": "285185c5183d9357"}, {"name": "module-hash", "duration": 56053, "timestamp": 43288174377, "id": 157, "parentId": 150, "tags": {}, "startTime": 1753816094277, "traceId": "285185c5183d9357"}, {"name": "code-generation", "duration": 23176, "timestamp": 43288230678, "id": 158, "parentId": 150, "tags": {}, "startTime": 1753816094333, "traceId": "285185c5183d9357"}, {"name": "hash", "duration": 15514, "timestamp": 43288263410, "id": 159, "parentId": 150, "tags": {}, "startTime": 1753816094366, "traceId": "285185c5183d9357"}, {"name": "code-generation-jobs", "duration": 381, "timestamp": 43288278919, "id": 160, "parentId": 150, "tags": {}, "startTime": 1753816094382, "traceId": "285185c5183d9357"}, {"name": "module-assets", "duration": 654, "timestamp": 43288279211, "id": 161, "parentId": 150, "tags": {}, "startTime": 1753816094382, "traceId": "285185c5183d9357"}, {"name": "create-chunk-assets", "duration": 2295, "timestamp": 43288279881, "id": 162, "parentId": 150, "tags": {}, "startTime": 1753816094382, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 587, "timestamp": 43288300457, "id": 164, "parentId": 163, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 358, "timestamp": 43288300695, "id": 165, "parentId": 163, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 343, "timestamp": 43288300712, "id": 166, "parentId": 163, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 329, "timestamp": 43288300726, "id": 167, "parentId": 163, "tags": {"name": "../app/activities/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 315, "timestamp": 43288300742, "id": 168, "parentId": 163, "tags": {"name": "../app/api/image/route.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 306, "timestamp": 43288300752, "id": 169, "parentId": 163, "tags": {"name": "../app/debug/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 302, "timestamp": 43288300760, "id": 170, "parentId": 163, "tags": {"name": "../app/favorites/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 293, "timestamp": 43288300770, "id": 171, "parentId": 163, "tags": {"name": "../app/messages/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 285, "timestamp": 43288300779, "id": 172, "parentId": 163, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 161, "timestamp": 43288300904, "id": 173, "parentId": 163, "tags": {"name": "../app/post/detail/page.js", "cache": "HIT"}, "startTime": 1753816094403, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 138, "timestamp": 43288300928, "id": 174, "parentId": 163, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 126, "timestamp": 43288300940, "id": 175, "parentId": 163, "tags": {"name": "../app/test-email/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 121, "timestamp": 43288300946, "id": 176, "parentId": 163, "tags": {"name": "../app/upload/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 117, "timestamp": 43288300951, "id": 177, "parentId": 163, "tags": {"name": "../app/profile/[id]/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 114, "timestamp": 43288300956, "id": 178, "parentId": 163, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 110, "timestamp": 43288300960, "id": 179, "parentId": 163, "tags": {"name": "../app/admin/activities/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 107, "timestamp": 43288300965, "id": 180, "parentId": 163, "tags": {"name": "../app/admin/ads/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 103, "timestamp": 43288300969, "id": 181, "parentId": 163, "tags": {"name": "../app/admin/dashboard/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 100, "timestamp": 43288300974, "id": 182, "parentId": 163, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 96, "timestamp": 43288300978, "id": 183, "parentId": 163, "tags": {"name": "../app/admin/performance/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 92, "timestamp": 43288300983, "id": 184, "parentId": 163, "tags": {"name": "../app/admin/posts/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 89, "timestamp": 43288300988, "id": 185, "parentId": 163, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 85, "timestamp": 43288300993, "id": 186, "parentId": 163, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 81, "timestamp": 43288300997, "id": 187, "parentId": 163, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 77, "timestamp": 43288301002, "id": 188, "parentId": 163, "tags": {"name": "746.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 74, "timestamp": 43288301006, "id": 189, "parentId": 163, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 71, "timestamp": 43288301011, "id": 190, "parentId": 163, "tags": {"name": "201.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 67, "timestamp": 43288301015, "id": 191, "parentId": 163, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 64, "timestamp": 43288301020, "id": 192, "parentId": 163, "tags": {"name": "240.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 60, "timestamp": 43288301024, "id": 193, "parentId": 163, "tags": {"name": "535.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 57, "timestamp": 43288301028, "id": 194, "parentId": 163, "tags": {"name": "792.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 53, "timestamp": 43288301033, "id": 195, "parentId": 163, "tags": {"name": "162.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "minify-js", "duration": 48, "timestamp": 43288301039, "id": 196, "parentId": 163, "tags": {"name": "203.js", "cache": "HIT"}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "terser-webpack-plugin-optimize", "duration": 10583, "timestamp": 43288290522, "id": 163, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1753816094393, "traceId": "285185c5183d9357"}, {"name": "css-minimizer-plugin", "duration": 166, "timestamp": 43288301238, "id": 197, "parentId": 19, "tags": {}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "create-trace-assets", "duration": 2606, "timestamp": 43288301625, "id": 198, "parentId": 20, "tags": {}, "startTime": 1753816094404, "traceId": "285185c5183d9357"}, {"name": "seal", "duration": 476032, "timestamp": 43287851470, "id": 150, "parentId": 19, "tags": {}, "startTime": 1753816093954, "traceId": "285185c5183d9357"}, {"name": "webpack-compilation", "duration": 3317648, "timestamp": 43285045790, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753816091148, "traceId": "285185c5183d9357"}, {"name": "emit", "duration": 73020, "timestamp": 43288364485, "id": 199, "parentId": 16, "tags": {}, "startTime": 1753816094467, "traceId": "285185c5183d9357"}, {"name": "webpack-close", "duration": 990, "timestamp": 43288438359, "id": 200, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753816094541, "traceId": "285185c5183d9357"}, {"name": "webpack-generate-error-stats", "duration": 3336, "timestamp": 43288439409, "id": 201, "parentId": 200, "tags": {}, "startTime": 1753816094542, "traceId": "285185c5183d9357"}, {"name": "run-webpack-compiler", "duration": 4483695, "timestamp": 43283959460, "id": 16, "parentId": 15, "tags": {}, "startTime": 1753816090062, "traceId": "285185c5183d9357"}, {"name": "format-webpack-messages", "duration": 99, "timestamp": 43288443161, "id": 202, "parentId": 15, "tags": {}, "startTime": 1753816094546, "traceId": "285185c5183d9357"}, {"name": "worker-main-server", "duration": 4484373, "timestamp": 43283959019, "id": 15, "parentId": 1, "tags": {}, "startTime": 1753816090062, "traceId": "285185c5183d9357"}, {"name": "create-entrypoints", "duration": 73889, "timestamp": 43289590890, "id": 206, "parentId": 204, "tags": {}, "startTime": 1753816095693, "traceId": "285185c5183d9357"}, {"name": "generate-webpack-config", "duration": 687178, "timestamp": 43289665076, "id": 207, "parentId": 205, "tags": {}, "startTime": 1753816095768, "traceId": "285185c5183d9357"}, {"name": "make", "duration": 2384, "timestamp": 43290486156, "id": 209, "parentId": 208, "tags": {}, "startTime": 1753816096589, "traceId": "285185c5183d9357"}, {"name": "chunk-graph", "duration": 860, "timestamp": 43290492645, "id": 211, "parentId": 210, "tags": {}, "startTime": 1753816096595, "traceId": "285185c5183d9357"}, {"name": "optimize-modules", "duration": 46, "timestamp": 43290493681, "id": 213, "parentId": 210, "tags": {}, "startTime": 1753816096596, "traceId": "285185c5183d9357"}]