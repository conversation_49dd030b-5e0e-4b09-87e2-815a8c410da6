[{"name": "generate-buildid", "duration": 93, "timestamp": 39405994696, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753812212097, "traceId": "0e8e66fe4568354f"}, {"name": "load-custom-routes", "duration": 624, "timestamp": 39405995126, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753812212098, "traceId": "0e8e66fe4568354f"}, {"name": "create-pages-mapping", "duration": 199, "timestamp": 39406093993, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753812212197, "traceId": "0e8e66fe4568354f"}, {"name": "collect-app-paths", "duration": 2763, "timestamp": 39406094224, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753812212197, "traceId": "0e8e66fe4568354f"}, {"name": "create-app-mapping", "duration": 2456, "timestamp": 39406097020, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753812212200, "traceId": "0e8e66fe4568354f"}, {"name": "public-dir-conflict-check", "duration": 681, "timestamp": 39406100313, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753812212203, "traceId": "0e8e66fe4568354f"}, {"name": "generate-routes-manifest", "duration": 4134, "timestamp": 39406101204, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753812212204, "traceId": "0e8e66fe4568354f"}, {"name": "create-dist-dir", "duration": 368, "timestamp": 39406106486, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753812212209, "traceId": "0e8e66fe4568354f"}, {"name": "write-routes-manifest", "duration": 5682, "timestamp": 39406179933, "id": 12, "parentId": 1, "tags": {}, "startTime": 1753812212283, "traceId": "0e8e66fe4568354f"}, {"name": "generate-required-server-files", "duration": 571, "timestamp": 39406185746, "id": 13, "parentId": 1, "tags": {}, "startTime": 1753812212288, "traceId": "0e8e66fe4568354f"}, {"name": "create-entrypoints", "duration": 100662, "timestamp": 39407579495, "id": 17, "parentId": 15, "tags": {}, "startTime": 1753812213682, "traceId": "0e8e66fe4568354f"}, {"name": "generate-webpack-config", "duration": 677936, "timestamp": 39407680413, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753812213783, "traceId": "0e8e66fe4568354f"}, {"name": "next-trace-entrypoint-plugin", "duration": 2903, "timestamp": 39408482856, "id": 20, "parentId": 19, "tags": {}, "startTime": 1753812214585, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 535754, "timestamp": 39408494573, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 549930, "timestamp": 39408494618, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 556626, "timestamp": 39408494646, "id": 25, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-transform", "duration": 6993, "timestamp": 39409293896, "id": 47, "parentId": 46, "tags": {}, "startTime": 1753812215396, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-loader", "duration": 8286, "timestamp": 39409292628, "id": 46, "parentId": 44, "tags": {}, "startTime": 1753812215395, "traceId": "0e8e66fe4568354f"}, {"name": "build-module-tsx", "duration": 28091, "timestamp": 39409284962, "id": 44, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\app\\page.tsx", "layer": "rsc"}, "startTime": 1753812215387, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-transform", "duration": 18934, "timestamp": 39409294175, "id": 49, "parentId": 48, "tags": {}, "startTime": 1753812215396, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-loader", "duration": 19153, "timestamp": 39409293976, "id": 48, "parentId": 45, "tags": {}, "startTime": 1753812215396, "traceId": "0e8e66fe4568354f"}, {"name": "build-module-tsx", "duration": 22256, "timestamp": 39409292084, "id": 45, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\app\\post\\detail\\page.tsx", "layer": "rsc"}, "startTime": 1753812215394, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 846492, "timestamp": 39408494003, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214596, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845897, "timestamp": 39408494665, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Factivities%2Fpage&name=app%2Factivities%2Fpage&pagePath=private-next-app-dir%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845925, "timestamp": 39408494698, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdebug%2Fpage&name=app%2Fdebug%2Fpage&pagePath=private-next-app-dir%2Fdebug%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fdebug%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845997, "timestamp": 39408494715, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffavorites%2Fpage&name=app%2Ffavorites%2Fpage&pagePath=private-next-app-dir%2Ffavorites%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ffavorites%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 846065, "timestamp": 39408494729, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fmessages%2Fpage&name=app%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fmessages%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845871, "timestamp": 39408494934, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845843, "timestamp": 39408494985, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpost%2Fdetail%2Fpage&name=app%2Fpost%2Fdetail%2Fpage&pagePath=private-next-app-dir%2Fpost%2Fdetail%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpost%2Fdetail%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845837, "timestamp": 39408494995, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845836, "timestamp": 39408495000, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftest-email%2Fpage&name=app%2Ftest-email%2Fpage&pagePath=private-next-app-dir%2Ftest-email%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ftest-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845836, "timestamp": 39408495004, "id": 35, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fupload%2Fpage&name=app%2Fupload%2Fpage&pagePath=private-next-app-dir%2Fupload%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fupload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845837, "timestamp": 39408495011, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fads%2Fpage&name=app%2Fadmin%2Fads%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fads%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fads%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845835, "timestamp": 39408495016, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factivities%2Fpage&name=app%2Fadmin%2Factivities%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845833, "timestamp": 39408495021, "id": 39, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fdashboard%2Fpage&name=app%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845829, "timestamp": 39408495029, "id": 41, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845828, "timestamp": 39408495033, "id": 42, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845843, "timestamp": 39408495024, "id": 40, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fposts%2Fpage&name=app%2Fadmin%2Fposts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fposts%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fposts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 845834, "timestamp": 39408495037, "id": 43, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fperformance%2Fpage&name=app%2Fadmin%2Fperformance%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fperformance%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fperformance%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 846541, "timestamp": 39408495008, "id": 36, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2F%5Bid%5D%2Fpage&name=app%2Fprofile%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprofile%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "add-entry", "duration": 1066195, "timestamp": 39408494684, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fimage%2Froute&name=app%2Fapi%2Fimage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fimage%2Froute.ts&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fapi%2Fimage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753812214597, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-transform", "duration": 11258, "timestamp": 39409669151, "id": 155, "parentId": 154, "tags": {}, "startTime": 1753812215771, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-loader", "duration": 11538, "timestamp": 39409668880, "id": 154, "parentId": 152, "tags": {}, "startTime": 1753812215771, "traceId": "0e8e66fe4568354f"}, {"name": "build-module-tsx", "duration": 35261, "timestamp": 39409666620, "id": 152, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\app\\page.tsx", "layer": "ssr"}, "startTime": 1753812215769, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-transform", "duration": 32813, "timestamp": 39409669284, "id": 157, "parentId": 156, "tags": {}, "startTime": 1753812215772, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-loader", "duration": 32939, "timestamp": 39409669162, "id": 156, "parentId": 153, "tags": {}, "startTime": 1753812215771, "traceId": "0e8e66fe4568354f"}, {"name": "build-module-tsx", "duration": 60336, "timestamp": 39409668687, "id": 153, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\app\\post\\detail\\page.tsx", "layer": "ssr"}, "startTime": 1753812215771, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-transform", "duration": 3586, "timestamp": 39410191728, "id": 160, "parentId": 159, "tags": {}, "startTime": 1753812216294, "traceId": "0e8e66fe4568354f"}, {"name": "next-swc-loader", "duration": 3862, "timestamp": 39410191473, "id": 159, "parentId": 158, "tags": {}, "startTime": 1753812216294, "traceId": "0e8e66fe4568354f"}, {"name": "build-module-ts", "duration": 10557, "timestamp": 39410190173, "id": 158, "parentId": 152, "tags": {"name": "D:\\web-cloudbase-project\\src\\hooks\\usePostFilters.ts", "layer": "ssr"}, "startTime": 1753812216292, "traceId": "0e8e66fe4568354f"}, {"name": "make", "duration": 2076812, "timestamp": 39408493242, "id": 21, "parentId": 19, "tags": {}, "startTime": 1753812214596, "traceId": "0e8e66fe4568354f"}, {"name": "get-entries", "duration": 7666, "timestamp": 39410571757, "id": 162, "parentId": 161, "tags": {}, "startTime": 1753812216674, "traceId": "0e8e66fe4568354f"}, {"name": "node-file-trace-plugin", "duration": 95587, "timestamp": 39410584584, "id": 163, "parentId": 161, "tags": {"traceEntryCount": "40"}, "startTime": 1753812216687, "traceId": "0e8e66fe4568354f"}, {"name": "collect-traced-files", "duration": 1230, "timestamp": 39410680185, "id": 164, "parentId": 161, "tags": {}, "startTime": 1753812216782, "traceId": "0e8e66fe4568354f"}, {"name": "finish-modules", "duration": 109958, "timestamp": 39410571467, "id": 161, "parentId": 20, "tags": {}, "startTime": 1753812216674, "traceId": "0e8e66fe4568354f"}, {"name": "chunk-graph", "duration": 50901, "timestamp": 39410795827, "id": 166, "parentId": 165, "tags": {}, "startTime": 1753812216898, "traceId": "0e8e66fe4568354f"}, {"name": "optimize-modules", "duration": 42, "timestamp": 39410846900, "id": 168, "parentId": 165, "tags": {}, "startTime": 1753812216949, "traceId": "0e8e66fe4568354f"}, {"name": "optimize-chunks", "duration": 77714, "timestamp": 39410847048, "id": 169, "parentId": 165, "tags": {}, "startTime": 1753812216949, "traceId": "0e8e66fe4568354f"}, {"name": "optimize-tree", "duration": 179, "timestamp": 39410924876, "id": 170, "parentId": 165, "tags": {}, "startTime": 1753812217027, "traceId": "0e8e66fe4568354f"}, {"name": "optimize-chunk-modules", "duration": 69323, "timestamp": 39410925173, "id": 171, "parentId": 165, "tags": {}, "startTime": 1753812217027, "traceId": "0e8e66fe4568354f"}, {"name": "optimize", "duration": 147837, "timestamp": 39410846824, "id": 167, "parentId": 165, "tags": {}, "startTime": 1753812216949, "traceId": "0e8e66fe4568354f"}, {"name": "module-hash", "duration": 55278, "timestamp": 39411037054, "id": 172, "parentId": 165, "tags": {}, "startTime": 1753812217139, "traceId": "0e8e66fe4568354f"}, {"name": "code-generation", "duration": 282560, "timestamp": 39411092422, "id": 173, "parentId": 165, "tags": {}, "startTime": 1753812217195, "traceId": "0e8e66fe4568354f"}, {"name": "hash", "duration": 14976, "timestamp": 39411385646, "id": 174, "parentId": 165, "tags": {}, "startTime": 1753812217488, "traceId": "0e8e66fe4568354f"}, {"name": "code-generation-jobs", "duration": 454, "timestamp": 39411400617, "id": 175, "parentId": 165, "tags": {}, "startTime": 1753812217503, "traceId": "0e8e66fe4568354f"}, {"name": "module-assets", "duration": 640, "timestamp": 39411400979, "id": 176, "parentId": 165, "tags": {}, "startTime": 1753812217503, "traceId": "0e8e66fe4568354f"}, {"name": "create-chunk-assets", "duration": 5479, "timestamp": 39411401635, "id": 177, "parentId": 165, "tags": {}, "startTime": 1753812217504, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21743, "timestamp": 39411428169, "id": 179, "parentId": 178, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1753812217530, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21533, "timestamp": 39411428391, "id": 180, "parentId": 178, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21517, "timestamp": 39411428409, "id": 181, "parentId": 178, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21503, "timestamp": 39411428424, "id": 182, "parentId": 178, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21489, "timestamp": 39411428439, "id": 183, "parentId": 178, "tags": {"name": "../app/activities/page.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21480, "timestamp": 39411428450, "id": 184, "parentId": 178, "tags": {"name": "../app/api/image/route.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21473, "timestamp": 39411428458, "id": 185, "parentId": 178, "tags": {"name": "../app/debug/page.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21465, "timestamp": 39411428468, "id": 186, "parentId": 178, "tags": {"name": "../app/favorites/page.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 21459, "timestamp": 39411428476, "id": 187, "parentId": 178, "tags": {"name": "../app/messages/page.js", "cache": "HIT"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 1715, "timestamp": 39411448222, "id": 190, "parentId": 178, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1753812217551, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 562, "timestamp": 39411449376, "id": 191, "parentId": 178, "tags": {"name": "../app/test-email/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 343, "timestamp": 39411449596, "id": 192, "parentId": 178, "tags": {"name": "../app/upload/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 315, "timestamp": 39411449626, "id": 193, "parentId": 178, "tags": {"name": "../app/profile/[id]/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 301, "timestamp": 39411449640, "id": 194, "parentId": 178, "tags": {"name": "../app/admin/ads/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 295, "timestamp": 39411449647, "id": 195, "parentId": 178, "tags": {"name": "../app/admin/activities/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 248, "timestamp": 39411449696, "id": 196, "parentId": 178, "tags": {"name": "../app/admin/dashboard/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 209, "timestamp": 39411449735, "id": 197, "parentId": 178, "tags": {"name": "../app/admin/posts/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 121, "timestamp": 39411449824, "id": 198, "parentId": 178, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 106, "timestamp": 39411449840, "id": 199, "parentId": 178, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 101, "timestamp": 39411449846, "id": 200, "parentId": 178, "tags": {"name": "../app/admin/performance/page.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 96, "timestamp": 39411449851, "id": 201, "parentId": 178, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 92, "timestamp": 39411449857, "id": 202, "parentId": 178, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 88, "timestamp": 39411449862, "id": 203, "parentId": 178, "tags": {"name": "746.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 83, "timestamp": 39411449867, "id": 204, "parentId": 178, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 79, "timestamp": 39411449873, "id": 205, "parentId": 178, "tags": {"name": "201.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 74, "timestamp": 39411449878, "id": 206, "parentId": 178, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 71, "timestamp": 39411449884, "id": 207, "parentId": 178, "tags": {"name": "240.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 67, "timestamp": 39411449889, "id": 208, "parentId": 178, "tags": {"name": "535.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 63, "timestamp": 39411449894, "id": 209, "parentId": 178, "tags": {"name": "792.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 58, "timestamp": 39411449900, "id": 210, "parentId": 178, "tags": {"name": "162.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 52, "timestamp": 39411449907, "id": 211, "parentId": 178, "tags": {"name": "203.js", "cache": "HIT"}, "startTime": 1753812217552, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 63519, "timestamp": 39411428605, "id": 188, "parentId": 178, "tags": {"name": "../app/page.js", "cache": "MISS"}, "startTime": 1753812217531, "traceId": "0e8e66fe4568354f"}, {"name": "minify-js", "duration": 195884, "timestamp": 39411438134, "id": 189, "parentId": 178, "tags": {"name": "../app/post/detail/page.js", "cache": "MISS"}, "startTime": 1753812217540, "traceId": "0e8e66fe4568354f"}, {"name": "terser-webpack-plugin-optimize", "duration": 220273, "timestamp": 39411413773, "id": 178, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1753812217516, "traceId": "0e8e66fe4568354f"}]