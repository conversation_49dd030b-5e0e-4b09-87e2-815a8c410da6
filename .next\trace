[{"name": "generate-buildid", "duration": 95, "timestamp": 40576022020, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753813382124, "traceId": "b85aea2b55f9821d"}, {"name": "load-custom-routes", "duration": 372, "timestamp": 40576022454, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753813382125, "traceId": "b85aea2b55f9821d"}, {"name": "create-pages-mapping", "duration": 216, "timestamp": 40576122692, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753813382225, "traceId": "b85aea2b55f9821d"}, {"name": "collect-app-paths", "duration": 2927, "timestamp": 40576122940, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753813382225, "traceId": "b85aea2b55f9821d"}, {"name": "create-app-mapping", "duration": 1022, "timestamp": 40576125902, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753813382228, "traceId": "b85aea2b55f9821d"}, {"name": "public-dir-conflict-check", "duration": 657, "timestamp": 40576127593, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753813382230, "traceId": "b85aea2b55f9821d"}, {"name": "generate-routes-manifest", "duration": 4877, "timestamp": 40576128443, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753813382231, "traceId": "b85aea2b55f9821d"}, {"name": "create-dist-dir", "duration": 378, "timestamp": 40576134400, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753813382237, "traceId": "b85aea2b55f9821d"}, {"name": "write-routes-manifest", "duration": 2067, "timestamp": 40576204467, "id": 12, "parentId": 1, "tags": {}, "startTime": 1753813382307, "traceId": "b85aea2b55f9821d"}, {"name": "generate-required-server-files", "duration": 561, "timestamp": 40576206661, "id": 13, "parentId": 1, "tags": {}, "startTime": 1753813382309, "traceId": "b85aea2b55f9821d"}, {"name": "create-entrypoints", "duration": 50170, "timestamp": 40577725289, "id": 17, "parentId": 15, "tags": {}, "startTime": 1753813383828, "traceId": "b85aea2b55f9821d"}, {"name": "generate-webpack-config", "duration": 666489, "timestamp": 40577775707, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753813383879, "traceId": "b85aea2b55f9821d"}, {"name": "next-trace-entrypoint-plugin", "duration": 2610, "timestamp": 40578570673, "id": 20, "parentId": 19, "tags": {}, "startTime": 1753813384674, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 586318, "timestamp": 40578583588, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 599776, "timestamp": 40578583633, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 606977, "timestamp": 40578584009, "id": 35, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859753, "timestamp": 40578582999, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384686, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859119, "timestamp": 40578583661, "id": 25, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Factivities%2Fpage&name=app%2Factivities%2Fpage&pagePath=private-next-app-dir%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859091, "timestamp": 40578583699, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdebug%2Fpage&name=app%2Fdebug%2Fpage&pagePath=private-next-app-dir%2Fdebug%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fdebug%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859185, "timestamp": 40578583713, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffavorites%2Fpage&name=app%2Ffavorites%2Fpage&pagePath=private-next-app-dir%2Ffavorites%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ffavorites%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859191, "timestamp": 40578583729, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fmessages%2Fpage&name=app%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fmessages%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859187, "timestamp": 40578583744, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859014, "timestamp": 40578583934, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpost%2Fdetail%2Fpage&name=app%2Fpost%2Fdetail%2Fpage&pagePath=private-next-app-dir%2Fpost%2Fdetail%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpost%2Fdetail%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858965, "timestamp": 40578583988, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftest-email%2Fpage&name=app%2Ftest-email%2Fpage&pagePath=private-next-app-dir%2Ftest-email%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ftest-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858958, "timestamp": 40578583998, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858954, "timestamp": 40578584006, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fupload%2Fpage&name=app%2Fupload%2Fpage&pagePath=private-next-app-dir%2Fupload%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fupload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858945, "timestamp": 40578584017, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factivities%2Fpage&name=app%2Fadmin%2Factivities%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858942, "timestamp": 40578584024, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fads%2Fpage&name=app%2Fadmin%2Fads%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fads%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fads%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858940, "timestamp": 40578584029, "id": 39, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fdashboard%2Fpage&name=app%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858940, "timestamp": 40578584032, "id": 40, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fperformance%2Fpage&name=app%2Fadmin%2Fperformance%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fperformance%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fperformance%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858937, "timestamp": 40578584039, "id": 41, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858936, "timestamp": 40578584043, "id": 42, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fposts%2Fpage&name=app%2Fadmin%2Fposts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fposts%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fposts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 858936, "timestamp": 40578584047, "id": 43, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 859377, "timestamp": 40578584014, "id": 36, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2F%5Bid%5D%2Fpage&name=app%2Fprofile%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprofile%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "add-entry", "duration": 1086759, "timestamp": 40578583682, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fimage%2Froute&name=app%2Fapi%2Fimage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fimage%2Froute.ts&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fapi%2Fimage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753813384687, "traceId": "b85aea2b55f9821d"}, {"name": "next-swc-transform", "duration": 9848, "timestamp": 40580239478, "id": 148, "parentId": 147, "tags": {}, "startTime": 1753813386343, "traceId": "b85aea2b55f9821d"}, {"name": "next-swc-loader", "duration": 11108, "timestamp": 40580238229, "id": 147, "parentId": 146, "tags": {}, "startTime": 1753813386341, "traceId": "b85aea2b55f9821d"}, {"name": "build-module-tsx", "duration": 54323, "timestamp": 40580221813, "id": 146, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\components\\home\\PetCard.tsx", "layer": "ssr"}, "startTime": 1753813386325, "traceId": "b85aea2b55f9821d"}, {"name": "make", "duration": 2123033, "timestamp": 40578582498, "id": 21, "parentId": 19, "tags": {}, "startTime": 1753813384686, "traceId": "b85aea2b55f9821d"}, {"name": "get-entries", "duration": 9171, "timestamp": 40580707317, "id": 150, "parentId": 149, "tags": {}, "startTime": 1753813386810, "traceId": "b85aea2b55f9821d"}, {"name": "node-file-trace-plugin", "duration": 92634, "timestamp": 40580720662, "id": 151, "parentId": 149, "tags": {"traceEntryCount": "40"}, "startTime": 1753813386824, "traceId": "b85aea2b55f9821d"}, {"name": "collect-traced-files", "duration": 2375, "timestamp": 40580813310, "id": 152, "parentId": 149, "tags": {}, "startTime": 1753813386916, "traceId": "b85aea2b55f9821d"}, {"name": "finish-modules", "duration": 108640, "timestamp": 40580707056, "id": 149, "parentId": 20, "tags": {}, "startTime": 1753813386810, "traceId": "b85aea2b55f9821d"}, {"name": "chunk-graph", "duration": 49952, "timestamp": 40580937122, "id": 154, "parentId": 153, "tags": {}, "startTime": 1753813387040, "traceId": "b85aea2b55f9821d"}, {"name": "optimize-modules", "duration": 42, "timestamp": 40580987250, "id": 156, "parentId": 153, "tags": {}, "startTime": 1753813387090, "traceId": "b85aea2b55f9821d"}, {"name": "optimize-chunks", "duration": 89381, "timestamp": 40580987401, "id": 157, "parentId": 153, "tags": {}, "startTime": 1753813387090, "traceId": "b85aea2b55f9821d"}, {"name": "optimize-tree", "duration": 169, "timestamp": 40581076896, "id": 158, "parentId": 153, "tags": {}, "startTime": 1753813387180, "traceId": "b85aea2b55f9821d"}, {"name": "optimize-chunk-modules", "duration": 73416, "timestamp": 40581077188, "id": 159, "parentId": 153, "tags": {}, "startTime": 1753813387180, "traceId": "b85aea2b55f9821d"}, {"name": "optimize", "duration": 163571, "timestamp": 40580987173, "id": 155, "parentId": 153, "tags": {}, "startTime": 1753813387090, "traceId": "b85aea2b55f9821d"}, {"name": "module-hash", "duration": 58969, "timestamp": 40581193114, "id": 160, "parentId": 153, "tags": {}, "startTime": 1753813387296, "traceId": "b85aea2b55f9821d"}, {"name": "code-generation", "duration": 42050, "timestamp": 40581252177, "id": 161, "parentId": 153, "tags": {}, "startTime": 1753813387355, "traceId": "b85aea2b55f9821d"}, {"name": "hash", "duration": 16937, "timestamp": 40581304161, "id": 162, "parentId": 153, "tags": {}, "startTime": 1753813387407, "traceId": "b85aea2b55f9821d"}, {"name": "code-generation-jobs", "duration": 842, "timestamp": 40581321093, "id": 163, "parentId": 153, "tags": {}, "startTime": 1753813387424, "traceId": "b85aea2b55f9821d"}, {"name": "module-assets", "duration": 1144, "timestamp": 40581321706, "id": 164, "parentId": 153, "tags": {}, "startTime": 1753813387425, "traceId": "b85aea2b55f9821d"}, {"name": "create-chunk-assets", "duration": 10265, "timestamp": 40581322887, "id": 165, "parentId": 153, "tags": {}, "startTime": 1753813387426, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6602, "timestamp": 40581356013, "id": 167, "parentId": 166, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6414, "timestamp": 40581356211, "id": 168, "parentId": 166, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6397, "timestamp": 40581356231, "id": 169, "parentId": 166, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6383, "timestamp": 40581356247, "id": 170, "parentId": 166, "tags": {"name": "../app/activities/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6471, "timestamp": 40581356260, "id": 171, "parentId": 166, "tags": {"name": "../app/api/image/route.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6464, "timestamp": 40581356270, "id": 172, "parentId": 166, "tags": {"name": "../app/debug/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6458, "timestamp": 40581356277, "id": 173, "parentId": 166, "tags": {"name": "../app/favorites/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6447, "timestamp": 40581356289, "id": 174, "parentId": 166, "tags": {"name": "../app/messages/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6440, "timestamp": 40581356298, "id": 175, "parentId": 166, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1753813387459, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6298, "timestamp": 40581356441, "id": 176, "parentId": 166, "tags": {"name": "../app/post/detail/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6276, "timestamp": 40581356464, "id": 177, "parentId": 166, "tags": {"name": "../app/test-email/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6266, "timestamp": 40581356476, "id": 178, "parentId": 166, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6261, "timestamp": 40581356481, "id": 179, "parentId": 166, "tags": {"name": "../app/upload/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6258, "timestamp": 40581356486, "id": 180, "parentId": 166, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6254, "timestamp": 40581356491, "id": 181, "parentId": 166, "tags": {"name": "../app/profile/[id]/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6251, "timestamp": 40581356495, "id": 182, "parentId": 166, "tags": {"name": "../app/admin/activities/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6248, "timestamp": 40581356500, "id": 183, "parentId": 166, "tags": {"name": "../app/admin/ads/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6244, "timestamp": 40581356504, "id": 184, "parentId": 166, "tags": {"name": "../app/admin/dashboard/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6238, "timestamp": 40581356512, "id": 185, "parentId": 166, "tags": {"name": "../app/admin/performance/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6234, "timestamp": 40581356518, "id": 186, "parentId": 166, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6230, "timestamp": 40581356522, "id": 187, "parentId": 166, "tags": {"name": "../app/admin/posts/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6227, "timestamp": 40581356526, "id": 188, "parentId": 166, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6224, "timestamp": 40581356531, "id": 189, "parentId": 166, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6221, "timestamp": 40581356535, "id": 190, "parentId": 166, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6218, "timestamp": 40581356540, "id": 191, "parentId": 166, "tags": {"name": "746.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6215, "timestamp": 40581356544, "id": 192, "parentId": 166, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6211, "timestamp": 40581356548, "id": 193, "parentId": 166, "tags": {"name": "201.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6208, "timestamp": 40581356553, "id": 194, "parentId": 166, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 6205, "timestamp": 40581356557, "id": 195, "parentId": 166, "tags": {"name": "240.js", "cache": "HIT"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 214, "timestamp": 40581362550, "id": 197, "parentId": 166, "tags": {"name": "792.js", "cache": "HIT"}, "startTime": 1753813387466, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 170, "timestamp": 40581362595, "id": 198, "parentId": 166, "tags": {"name": "162.js", "cache": "HIT"}, "startTime": 1753813387466, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 158, "timestamp": 40581362607, "id": 199, "parentId": 166, "tags": {"name": "203.js", "cache": "HIT"}, "startTime": 1753813387466, "traceId": "b85aea2b55f9821d"}, {"name": "minify-js", "duration": 20040, "timestamp": 40581356561, "id": 196, "parentId": 166, "tags": {"name": "535.js", "cache": "MISS"}, "startTime": 1753813387460, "traceId": "b85aea2b55f9821d"}, {"name": "terser-webpack-plugin-optimize", "duration": 35557, "timestamp": 40581341062, "id": 166, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1753813387444, "traceId": "b85aea2b55f9821d"}, {"name": "css-minimizer-plugin", "duration": 189, "timestamp": 40581376776, "id": 200, "parentId": 19, "tags": {}, "startTime": 1753813387480, "traceId": "b85aea2b55f9821d"}, {"name": "create-trace-assets", "duration": 1684, "timestamp": 40581377212, "id": 201, "parentId": 20, "tags": {}, "startTime": 1753813387480, "traceId": "b85aea2b55f9821d"}, {"name": "seal", "duration": 530168, "timestamp": 40580871115, "id": 153, "parentId": 19, "tags": {}, "startTime": 1753813386974, "traceId": "b85aea2b55f9821d"}, {"name": "webpack-compilation", "duration": 2871518, "timestamp": 40578568419, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753813384672, "traceId": "b85aea2b55f9821d"}, {"name": "emit", "duration": 65407, "timestamp": 40581440646, "id": 202, "parentId": 16, "tags": {}, "startTime": 1753813387544, "traceId": "b85aea2b55f9821d"}, {"name": "webpack-close", "duration": 137999, "timestamp": 40581506843, "id": 203, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753813387610, "traceId": "b85aea2b55f9821d"}, {"name": "webpack-generate-error-stats", "duration": 4616, "timestamp": 40581644919, "id": 204, "parentId": 203, "tags": {}, "startTime": 1753813387748, "traceId": "b85aea2b55f9821d"}, {"name": "run-webpack-compiler", "duration": 3924635, "timestamp": 40577725284, "id": 16, "parentId": 15, "tags": {}, "startTime": 1753813383828, "traceId": "b85aea2b55f9821d"}, {"name": "format-webpack-messages", "duration": 103, "timestamp": 40581649929, "id": 205, "parentId": 15, "tags": {}, "startTime": 1753813387753, "traceId": "b85aea2b55f9821d"}, {"name": "worker-main-server", "duration": 3925276, "timestamp": 40577724900, "id": 15, "parentId": 1, "tags": {}, "startTime": 1753813383828, "traceId": "b85aea2b55f9821d"}, {"name": "create-entrypoints", "duration": 100933, "timestamp": 40582836243, "id": 209, "parentId": 207, "tags": {}, "startTime": 1753813388939, "traceId": "b85aea2b55f9821d"}, {"name": "generate-webpack-config", "duration": 684146, "timestamp": 40582937428, "id": 210, "parentId": 208, "tags": {}, "startTime": 1753813389041, "traceId": "b85aea2b55f9821d"}]