[{"name": "generate-buildid", "duration": 747, "timestamp": 46538636660, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753819344739, "traceId": "0d01bd9a6934f197"}, {"name": "load-custom-routes", "duration": 384, "timestamp": 46538637813, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753819344741, "traceId": "0d01bd9a6934f197"}, {"name": "create-pages-mapping", "duration": 229, "timestamp": 46538728306, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753819344831, "traceId": "0d01bd9a6934f197"}, {"name": "collect-app-paths", "duration": 3206, "timestamp": 46538728571, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753819344831, "traceId": "0d01bd9a6934f197"}, {"name": "create-app-mapping", "duration": 1035, "timestamp": 46538731811, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753819344834, "traceId": "0d01bd9a6934f197"}, {"name": "public-dir-conflict-check", "duration": 845, "timestamp": 46538733504, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753819344836, "traceId": "0d01bd9a6934f197"}, {"name": "generate-routes-manifest", "duration": 4296, "timestamp": 46538734536, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753819344837, "traceId": "0d01bd9a6934f197"}, {"name": "create-dist-dir", "duration": 376, "timestamp": 46538739995, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753819344843, "traceId": "0d01bd9a6934f197"}, {"name": "write-routes-manifest", "duration": 12105, "timestamp": 46538833738, "id": 12, "parentId": 1, "tags": {}, "startTime": 1753819344936, "traceId": "0d01bd9a6934f197"}, {"name": "generate-required-server-files", "duration": 619, "timestamp": 46538845991, "id": 13, "parentId": 1, "tags": {}, "startTime": 1753819344949, "traceId": "0d01bd9a6934f197"}, {"name": "create-entrypoints", "duration": 98357, "timestamp": 46540576980, "id": 17, "parentId": 15, "tags": {}, "startTime": 1753819346680, "traceId": "0d01bd9a6934f197"}, {"name": "generate-webpack-config", "duration": 653993, "timestamp": 46540675583, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753819346779, "traceId": "0d01bd9a6934f197"}, {"name": "next-trace-entrypoint-plugin", "duration": 2879, "timestamp": 46541450628, "id": 20, "parentId": 19, "tags": {}, "startTime": 1753819347554, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 559711, "timestamp": 46541462513, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 571856, "timestamp": 46541462553, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 583780, "timestamp": 46541462581, "id": 25, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 819518, "timestamp": 46541461931, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347565, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818925, "timestamp": 46541462599, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Factivities%2Fpage&name=app%2Factivities%2Fpage&pagePath=private-next-app-dir%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818908, "timestamp": 46541462630, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdebug%2Fpage&name=app%2Fdebug%2Fpage&pagePath=private-next-app-dir%2Fdebug%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fdebug%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818978, "timestamp": 46541462646, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffavorites%2Fpage&name=app%2Ffavorites%2Fpage&pagePath=private-next-app-dir%2Ffavorites%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ffavorites%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818986, "timestamp": 46541462659, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fmessages%2Fpage&name=app%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fmessages%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818812, "timestamp": 46541462844, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818774, "timestamp": 46541462896, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpost%2Fdetail%2Fpage&name=app%2Fpost%2Fdetail%2Fpage&pagePath=private-next-app-dir%2Fpost%2Fdetail%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpost%2Fdetail%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818767, "timestamp": 46541462908, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818765, "timestamp": 46541462913, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftest-email%2Fpage&name=app%2Ftest-email%2Fpage&pagePath=private-next-app-dir%2Ftest-email%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ftest-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818765, "timestamp": 46541462916, "id": 35, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fupload%2Fpage&name=app%2Fupload%2Fpage&pagePath=private-next-app-dir%2Fupload%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fupload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818763, "timestamp": 46541462924, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factivities%2Fpage&name=app%2Fadmin%2Factivities%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818761, "timestamp": 46541462929, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fads%2Fpage&name=app%2Fadmin%2Fads%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fads%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fads%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818759, "timestamp": 46541462934, "id": 39, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fdashboard%2Fpage&name=app%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818759, "timestamp": 46541462938, "id": 40, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818757, "timestamp": 46541462942, "id": 41, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fperformance%2Fpage&name=app%2Fadmin%2Fperformance%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fperformance%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fperformance%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818757, "timestamp": 46541462946, "id": 42, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fposts%2Fpage&name=app%2Fadmin%2Fposts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fposts%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fposts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 818758, "timestamp": 46541462951, "id": 43, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 819157, "timestamp": 46541462921, "id": 36, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2F%5Bid%5D%2Fpage&name=app%2Fprofile%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprofile%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "add-entry", "duration": 1083635, "timestamp": 46541462617, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fimage%2Froute&name=app%2Fapi%2Fimage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fimage%2Froute.ts&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fapi%2Fimage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753819347566, "traceId": "0d01bd9a6934f197"}, {"name": "make", "duration": 2027755, "timestamp": 46541461459, "id": 21, "parentId": 19, "tags": {}, "startTime": 1753819347565, "traceId": "0d01bd9a6934f197"}, {"name": "get-entries", "duration": 7693, "timestamp": 46543490908, "id": 147, "parentId": 146, "tags": {}, "startTime": 1753819349594, "traceId": "0d01bd9a6934f197"}, {"name": "node-file-trace-plugin", "duration": 94027, "timestamp": 46543502469, "id": 148, "parentId": 146, "tags": {"traceEntryCount": "40"}, "startTime": 1753819349606, "traceId": "0d01bd9a6934f197"}, {"name": "collect-traced-files", "duration": 935, "timestamp": 46543596510, "id": 149, "parentId": 146, "tags": {}, "startTime": 1753819349700, "traceId": "0d01bd9a6934f197"}, {"name": "finish-modules", "duration": 106800, "timestamp": 46543490653, "id": 146, "parentId": 20, "tags": {}, "startTime": 1753819349594, "traceId": "0d01bd9a6934f197"}, {"name": "chunk-graph", "duration": 43131, "timestamp": 46543730156, "id": 151, "parentId": 150, "tags": {}, "startTime": 1753819349833, "traceId": "0d01bd9a6934f197"}, {"name": "optimize-modules", "duration": 42, "timestamp": 46543773463, "id": 153, "parentId": 150, "tags": {}, "startTime": 1753819349877, "traceId": "0d01bd9a6934f197"}, {"name": "optimize-chunks", "duration": 79893, "timestamp": 46543773613, "id": 154, "parentId": 150, "tags": {}, "startTime": 1753819349877, "traceId": "0d01bd9a6934f197"}, {"name": "optimize-tree", "duration": 176, "timestamp": 46543853626, "id": 155, "parentId": 150, "tags": {}, "startTime": 1753819349957, "traceId": "0d01bd9a6934f197"}, {"name": "optimize-chunk-modules", "duration": 66084, "timestamp": 46543854036, "id": 156, "parentId": 150, "tags": {}, "startTime": 1753819349957, "traceId": "0d01bd9a6934f197"}, {"name": "optimize", "duration": 146869, "timestamp": 46543773389, "id": 152, "parentId": 150, "tags": {}, "startTime": 1753819349877, "traceId": "0d01bd9a6934f197"}, {"name": "module-hash", "duration": 53959, "timestamp": 46543960596, "id": 157, "parentId": 150, "tags": {}, "startTime": 1753819350064, "traceId": "0d01bd9a6934f197"}, {"name": "code-generation", "duration": 20904, "timestamp": 46544014650, "id": 158, "parentId": 150, "tags": {}, "startTime": 1753819350118, "traceId": "0d01bd9a6934f197"}, {"name": "hash", "duration": 12313, "timestamp": 46544044664, "id": 159, "parentId": 150, "tags": {}, "startTime": 1753819350148, "traceId": "0d01bd9a6934f197"}, {"name": "code-generation-jobs", "duration": 356, "timestamp": 46544056973, "id": 160, "parentId": 150, "tags": {}, "startTime": 1753819350160, "traceId": "0d01bd9a6934f197"}, {"name": "module-assets", "duration": 759, "timestamp": 46544057246, "id": 161, "parentId": 150, "tags": {}, "startTime": 1753819350161, "traceId": "0d01bd9a6934f197"}, {"name": "create-chunk-assets", "duration": 2607, "timestamp": 46544058056, "id": 162, "parentId": 150, "tags": {}, "startTime": 1753819350161, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 559, "timestamp": 46544079158, "id": 164, "parentId": 163, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1753819350182, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 352, "timestamp": 46544079374, "id": 165, "parentId": 163, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 335, "timestamp": 46544079393, "id": 166, "parentId": 163, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 321, "timestamp": 46544079408, "id": 167, "parentId": 163, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 307, "timestamp": 46544079423, "id": 168, "parentId": 163, "tags": {"name": "../app/activities/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 298, "timestamp": 46544079433, "id": 169, "parentId": 163, "tags": {"name": "../app/api/image/route.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 293, "timestamp": 46544079440, "id": 170, "parentId": 163, "tags": {"name": "../app/debug/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 286, "timestamp": 46544079449, "id": 171, "parentId": 163, "tags": {"name": "../app/favorites/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 279, "timestamp": 46544079457, "id": 172, "parentId": 163, "tags": {"name": "../app/messages/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 160, "timestamp": 46544079577, "id": 173, "parentId": 163, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 136, "timestamp": 46544079602, "id": 174, "parentId": 163, "tags": {"name": "../app/post/detail/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 124, "timestamp": 46544079615, "id": 175, "parentId": 163, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 119, "timestamp": 46544079621, "id": 176, "parentId": 163, "tags": {"name": "../app/test-email/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 114, "timestamp": 46544079626, "id": 177, "parentId": 163, "tags": {"name": "../app/upload/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 111, "timestamp": 46544079631, "id": 178, "parentId": 163, "tags": {"name": "../app/profile/[id]/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 107, "timestamp": 46544079635, "id": 179, "parentId": 163, "tags": {"name": "../app/admin/activities/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 103, "timestamp": 46544079640, "id": 180, "parentId": 163, "tags": {"name": "../app/admin/ads/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 100, "timestamp": 46544079644, "id": 181, "parentId": 163, "tags": {"name": "../app/admin/dashboard/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 96, "timestamp": 46544079649, "id": 182, "parentId": 163, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 93, "timestamp": 46544079653, "id": 183, "parentId": 163, "tags": {"name": "../app/admin/performance/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 89, "timestamp": 46544079658, "id": 184, "parentId": 163, "tags": {"name": "../app/admin/posts/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 86, "timestamp": 46544079662, "id": 185, "parentId": 163, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 82, "timestamp": 46544079666, "id": 186, "parentId": 163, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 79, "timestamp": 46544079671, "id": 187, "parentId": 163, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 75, "timestamp": 46544079675, "id": 188, "parentId": 163, "tags": {"name": "746.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 72, "timestamp": 46544079680, "id": 189, "parentId": 163, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 68, "timestamp": 46544079684, "id": 190, "parentId": 163, "tags": {"name": "201.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 65, "timestamp": 46544079688, "id": 191, "parentId": 163, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 62, "timestamp": 46544079692, "id": 192, "parentId": 163, "tags": {"name": "240.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 58, "timestamp": 46544079697, "id": 193, "parentId": 163, "tags": {"name": "535.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 54, "timestamp": 46544079701, "id": 194, "parentId": 163, "tags": {"name": "792.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 51, "timestamp": 46544079706, "id": 195, "parentId": 163, "tags": {"name": "162.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "minify-js", "duration": 46, "timestamp": 46544079712, "id": 196, "parentId": 163, "tags": {"name": "203.js", "cache": "HIT"}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "terser-webpack-plugin-optimize", "duration": 11176, "timestamp": 46544068597, "id": 163, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1753819350172, "traceId": "0d01bd9a6934f197"}, {"name": "css-minimizer-plugin", "duration": 166, "timestamp": 46544079934, "id": 197, "parentId": 19, "tags": {}, "startTime": 1753819350183, "traceId": "0d01bd9a6934f197"}, {"name": "create-trace-assets", "duration": 1487, "timestamp": 46544080328, "id": 198, "parentId": 20, "tags": {}, "startTime": 1753819350184, "traceId": "0d01bd9a6934f197"}, {"name": "seal", "duration": 448227, "timestamp": 46543654418, "id": 150, "parentId": 19, "tags": {}, "startTime": 1753819349758, "traceId": "0d01bd9a6934f197"}, {"name": "webpack-compilation", "duration": 2720766, "timestamp": 46541447807, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753819347551, "traceId": "0d01bd9a6934f197"}, {"name": "emit", "duration": 65912, "timestamp": 46544169352, "id": 199, "parentId": 16, "tags": {}, "startTime": 1753819350273, "traceId": "0d01bd9a6934f197"}, {"name": "webpack-close", "duration": 1476, "timestamp": 46544236245, "id": 200, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753819350340, "traceId": "0d01bd9a6934f197"}, {"name": "webpack-generate-error-stats", "duration": 3523, "timestamp": 46544237813, "id": 201, "parentId": 200, "tags": {}, "startTime": 1753819350341, "traceId": "0d01bd9a6934f197"}, {"name": "run-webpack-compiler", "duration": 3664840, "timestamp": 46540576973, "id": 16, "parentId": 15, "tags": {}, "startTime": 1753819346680, "traceId": "0d01bd9a6934f197"}, {"name": "format-webpack-messages", "duration": 109, "timestamp": 46544241824, "id": 202, "parentId": 15, "tags": {}, "startTime": 1753819350345, "traceId": "0d01bd9a6934f197"}, {"name": "worker-main-server", "duration": 3665536, "timestamp": 46540576540, "id": 15, "parentId": 1, "tags": {}, "startTime": 1753819346680, "traceId": "0d01bd9a6934f197"}, {"name": "create-entrypoints", "duration": 86250, "timestamp": 46545622958, "id": 206, "parentId": 204, "tags": {}, "startTime": 1753819351726, "traceId": "0d01bd9a6934f197"}, {"name": "generate-webpack-config", "duration": 691730, "timestamp": 46545709661, "id": 207, "parentId": 205, "tags": {}, "startTime": 1753819351812, "traceId": "0d01bd9a6934f197"}, {"name": "make", "duration": 1062, "timestamp": 46546535748, "id": 209, "parentId": 208, "tags": {}, "startTime": 1753819352638, "traceId": "0d01bd9a6934f197"}, {"name": "chunk-graph", "duration": 935, "timestamp": 46546541482, "id": 211, "parentId": 210, "tags": {}, "startTime": 1753819352644, "traceId": "0d01bd9a6934f197"}, {"name": "optimize-modules", "duration": 59, "timestamp": 46546542662, "id": 213, "parentId": 210, "tags": {}, "startTime": 1753819352645, "traceId": "0d01bd9a6934f197"}]