[{"name": "generate-buildid", "duration": 98, "timestamp": 38599131277, "id": 4, "parentId": 1, "tags": {}, "startTime": 1753811405234, "traceId": "a85d0bbb16d5e901"}, {"name": "load-custom-routes", "duration": 375, "timestamp": 38599131722, "id": 5, "parentId": 1, "tags": {}, "startTime": 1753811405235, "traceId": "a85d0bbb16d5e901"}, {"name": "create-pages-mapping", "duration": 215, "timestamp": 38599232434, "id": 6, "parentId": 1, "tags": {}, "startTime": 1753811405335, "traceId": "a85d0bbb16d5e901"}, {"name": "collect-app-paths", "duration": 3949, "timestamp": 38599232684, "id": 7, "parentId": 1, "tags": {}, "startTime": 1753811405336, "traceId": "a85d0bbb16d5e901"}, {"name": "create-app-mapping", "duration": 1098, "timestamp": 38599236671, "id": 8, "parentId": 1, "tags": {}, "startTime": 1753811405340, "traceId": "a85d0bbb16d5e901"}, {"name": "public-dir-conflict-check", "duration": 682, "timestamp": 38599238519, "id": 9, "parentId": 1, "tags": {}, "startTime": 1753811405342, "traceId": "a85d0bbb16d5e901"}, {"name": "generate-routes-manifest", "duration": 4459, "timestamp": 38599239408, "id": 10, "parentId": 1, "tags": {}, "startTime": 1753811405342, "traceId": "a85d0bbb16d5e901"}, {"name": "create-dist-dir", "duration": 498, "timestamp": 38599244972, "id": 11, "parentId": 1, "tags": {}, "startTime": 1753811405348, "traceId": "a85d0bbb16d5e901"}, {"name": "write-routes-manifest", "duration": 1083, "timestamp": 38599253273, "id": 12, "parentId": 1, "tags": {}, "startTime": 1753811405356, "traceId": "a85d0bbb16d5e901"}, {"name": "generate-required-server-files", "duration": 541, "timestamp": 38599254538, "id": 13, "parentId": 1, "tags": {}, "startTime": 1753811405358, "traceId": "a85d0bbb16d5e901"}, {"name": "create-entrypoints", "duration": 101136, "timestamp": 38600541995, "id": 17, "parentId": 15, "tags": {}, "startTime": 1753811406645, "traceId": "a85d0bbb16d5e901"}, {"name": "generate-webpack-config", "duration": 767093, "timestamp": 38600643379, "id": 18, "parentId": 16, "tags": {}, "startTime": 1753811406746, "traceId": "a85d0bbb16d5e901"}, {"name": "next-trace-entrypoint-plugin", "duration": 4821, "timestamp": 38601632492, "id": 20, "parentId": 19, "tags": {}, "startTime": 1753811407735, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1068903, "timestamp": 38601652518, "id": 23, "parentId": 21, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1753811407755, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1114570, "timestamp": 38601652938, "id": 24, "parentId": 21, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1139938, "timestamp": 38601652994, "id": 25, "parentId": 21, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1442875, "timestamp": 38601651649, "id": 22, "parentId": 21, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407755, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441529, "timestamp": 38601653026, "id": 26, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Factivities%2Fpage&name=app%2Factivities%2Fpage&pagePath=private-next-app-dir%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441496, "timestamp": 38601653070, "id": 28, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fdebug%2Fpage&name=app%2Fdebug%2Fpage&pagePath=private-next-app-dir%2Fdebug%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fdebug%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441504, "timestamp": 38601653088, "id": 29, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ffavorites%2Fpage&name=app%2Ffavorites%2Fpage&pagePath=private-next-app-dir%2Ffavorites%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ffavorites%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441510, "timestamp": 38601653103, "id": 30, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fmessages%2Fpage&name=app%2Fmessages%2Fpage&pagePath=private-next-app-dir%2Fmessages%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fmessages%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441018, "timestamp": 38601653607, "id": 31, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441033, "timestamp": 38601653687, "id": 32, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fpost%2Fdetail%2Fpage&name=app%2Fpost%2Fdetail%2Fpage&pagePath=private-next-app-dir%2Fpost%2Fdetail%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fpost%2Fdetail%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441013, "timestamp": 38601653714, "id": 33, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441006, "timestamp": 38601653725, "id": 34, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Ftest-email%2Fpage&name=app%2Ftest-email%2Fpage&pagePath=private-next-app-dir%2Ftest-email%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Ftest-email%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441003, "timestamp": 38601653731, "id": 35, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fupload%2Fpage&name=app%2Fupload%2Fpage&pagePath=private-next-app-dir%2Fupload%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fupload%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440996, "timestamp": 38601653741, "id": 37, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Factivities%2Fpage&name=app%2Fadmin%2Factivities%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Factivities%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Factivities%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440992, "timestamp": 38601653748, "id": 38, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fads%2Fpage&name=app%2Fadmin%2Fads%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fads%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fads%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440991, "timestamp": 38601653753, "id": 39, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fdashboard%2Fpage&name=app%2Fadmin%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fdashboard%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440989, "timestamp": 38601653759, "id": 40, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fpage&name=app%2Fadmin%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440987, "timestamp": 38601653763, "id": 41, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fperformance%2Fpage&name=app%2Fadmin%2Fperformance%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fperformance%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fperformance%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440984, "timestamp": 38601653770, "id": 42, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fposts%2Fpage&name=app%2Fadmin%2Fposts%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fposts%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fposts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1440985, "timestamp": 38601653775, "id": 43, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fadmin%2Fsettings%2Fpage&name=app%2Fadmin%2Fsettings%2Fpage&pagePath=private-next-app-dir%2Fadmin%2Fsettings%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fadmin%2Fsettings%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1441036, "timestamp": 38601653734, "id": 36, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fprofile%2F%5Bid%5D%2Fpage&name=app%2Fprofile%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fprofile%2F%5Bid%5D%2Fpage.tsx&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fprofile%2F%5Bid%5D%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407757, "traceId": "a85d0bbb16d5e901"}, {"name": "add-entry", "duration": 1672769, "timestamp": 38601653053, "id": 27, "parentId": 21, "tags": {"request": "next-app-loader?page=%2Fapi%2Fimage%2Froute&name=app%2Fapi%2Fimage%2Froute&pagePath=private-next-app-dir%2Fapi%2Fimage%2Froute.ts&appDir=D%3A%5Cweb-cloudbase-project%5Csrc%5Capp&appPaths=%2Fapi%2Fimage%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=%2Fpet-trading-platform&assetPrefix=%2Fpet-trading-platform%2F&nextConfigOutput=export&nextConfigExperimentalUseEarlyImport=false&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1753811407756, "traceId": "a85d0bbb16d5e901"}, {"name": "next-swc-transform", "duration": 27803, "timestamp": 38604136406, "id": 148, "parentId": 147, "tags": {}, "startTime": 1753811410239, "traceId": "a85d0bbb16d5e901"}, {"name": "next-swc-loader", "duration": 29095, "timestamp": 38604135127, "id": 147, "parentId": 146, "tags": {}, "startTime": 1753811410238, "traceId": "a85d0bbb16d5e901"}, {"name": "build-module-tsx", "duration": 86267, "timestamp": 38604126703, "id": 146, "parentId": 19, "tags": {"name": "D:\\web-cloudbase-project\\src\\components\\notifications\\NotificationModal.tsx", "layer": "ssr"}, "startTime": 1753811410230, "traceId": "a85d0bbb16d5e901"}, {"name": "make", "duration": 2877885, "timestamp": 38601650412, "id": 21, "parentId": 19, "tags": {}, "startTime": 1753811407753, "traceId": "a85d0bbb16d5e901"}, {"name": "get-entries", "duration": 7944, "timestamp": 38604530049, "id": 150, "parentId": 149, "tags": {}, "startTime": 1753811410633, "traceId": "a85d0bbb16d5e901"}, {"name": "node-file-trace-plugin", "duration": 102809, "timestamp": 38604544182, "id": 151, "parentId": 149, "tags": {"traceEntryCount": "40"}, "startTime": 1753811410647, "traceId": "a85d0bbb16d5e901"}, {"name": "collect-traced-files", "duration": 908, "timestamp": 38604647005, "id": 152, "parentId": 149, "tags": {}, "startTime": 1753811410750, "traceId": "a85d0bbb16d5e901"}, {"name": "finish-modules", "duration": 118135, "timestamp": 38604529787, "id": 149, "parentId": 20, "tags": {}, "startTime": 1753811410633, "traceId": "a85d0bbb16d5e901"}, {"name": "chunk-graph", "duration": 49707, "timestamp": 38604798251, "id": 154, "parentId": 153, "tags": {}, "startTime": 1753811410901, "traceId": "a85d0bbb16d5e901"}, {"name": "optimize-modules", "duration": 48, "timestamp": 38604848131, "id": 156, "parentId": 153, "tags": {}, "startTime": 1753811410951, "traceId": "a85d0bbb16d5e901"}, {"name": "optimize-chunks", "duration": 91446, "timestamp": 38604848293, "id": 157, "parentId": 153, "tags": {}, "startTime": 1753811410951, "traceId": "a85d0bbb16d5e901"}, {"name": "optimize-tree", "duration": 183, "timestamp": 38604939863, "id": 158, "parentId": 153, "tags": {}, "startTime": 1753811411043, "traceId": "a85d0bbb16d5e901"}, {"name": "optimize-chunk-modules", "duration": 76996, "timestamp": 38604940164, "id": 159, "parentId": 153, "tags": {}, "startTime": 1753811411043, "traceId": "a85d0bbb16d5e901"}, {"name": "optimize", "duration": 169323, "timestamp": 38604848057, "id": 155, "parentId": 153, "tags": {}, "startTime": 1753811410951, "traceId": "a85d0bbb16d5e901"}, {"name": "module-hash", "duration": 85405, "timestamp": 38605127930, "id": 160, "parentId": 153, "tags": {}, "startTime": 1753811411231, "traceId": "a85d0bbb16d5e901"}, {"name": "code-generation", "duration": 82125, "timestamp": 38605213453, "id": 161, "parentId": 153, "tags": {}, "startTime": 1753811411316, "traceId": "a85d0bbb16d5e901"}, {"name": "hash", "duration": 13937, "timestamp": 38605307139, "id": 162, "parentId": 153, "tags": {}, "startTime": 1753811411410, "traceId": "a85d0bbb16d5e901"}, {"name": "code-generation-jobs", "duration": 390, "timestamp": 38605321071, "id": 163, "parentId": 153, "tags": {}, "startTime": 1753811411424, "traceId": "a85d0bbb16d5e901"}, {"name": "module-assets", "duration": 688, "timestamp": 38605321373, "id": 164, "parentId": 153, "tags": {}, "startTime": 1753811411424, "traceId": "a85d0bbb16d5e901"}, {"name": "create-chunk-assets", "duration": 6051, "timestamp": 38605322081, "id": 165, "parentId": 153, "tags": {}, "startTime": 1753811411425, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9964, "timestamp": 38605349529, "id": 167, "parentId": 166, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1753811411452, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9752, "timestamp": 38605349751, "id": 168, "parentId": 166, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9737, "timestamp": 38605349769, "id": 169, "parentId": 166, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9723, "timestamp": 38605349784, "id": 170, "parentId": 166, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9718, "timestamp": 38605349798, "id": 171, "parentId": 166, "tags": {"name": "../app/activities/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9709, "timestamp": 38605349810, "id": 172, "parentId": 166, "tags": {"name": "../app/api/image/route.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9701, "timestamp": 38605349819, "id": 173, "parentId": 166, "tags": {"name": "../app/debug/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9689, "timestamp": 38605349833, "id": 174, "parentId": 166, "tags": {"name": "../app/favorites/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9681, "timestamp": 38605349842, "id": 175, "parentId": 166, "tags": {"name": "../app/messages/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9556, "timestamp": 38605349969, "id": 176, "parentId": 166, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9532, "timestamp": 38605349994, "id": 177, "parentId": 166, "tags": {"name": "../app/post/detail/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9520, "timestamp": 38605350007, "id": 178, "parentId": 166, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9515, "timestamp": 38605350014, "id": 179, "parentId": 166, "tags": {"name": "../app/test-email/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9511, "timestamp": 38605350019, "id": 180, "parentId": 166, "tags": {"name": "../app/upload/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9507, "timestamp": 38605350024, "id": 181, "parentId": 166, "tags": {"name": "../app/profile/[id]/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9504, "timestamp": 38605350029, "id": 182, "parentId": 166, "tags": {"name": "../app/admin/activities/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9500, "timestamp": 38605350034, "id": 183, "parentId": 166, "tags": {"name": "../app/admin/ads/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9496, "timestamp": 38605350039, "id": 184, "parentId": 166, "tags": {"name": "../app/admin/dashboard/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9493, "timestamp": 38605350044, "id": 185, "parentId": 166, "tags": {"name": "../app/admin/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9489, "timestamp": 38605350049, "id": 186, "parentId": 166, "tags": {"name": "../app/admin/performance/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9486, "timestamp": 38605350054, "id": 187, "parentId": 166, "tags": {"name": "../app/admin/posts/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9482, "timestamp": 38605350059, "id": 188, "parentId": 166, "tags": {"name": "../app/admin/settings/page.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9479, "timestamp": 38605350064, "id": 189, "parentId": 166, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9476, "timestamp": 38605350069, "id": 190, "parentId": 166, "tags": {"name": "145.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9472, "timestamp": 38605350073, "id": 191, "parentId": 166, "tags": {"name": "746.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9469, "timestamp": 38605350078, "id": 192, "parentId": 166, "tags": {"name": "276.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9465, "timestamp": 38605350083, "id": 193, "parentId": 166, "tags": {"name": "201.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9462, "timestamp": 38605350088, "id": 194, "parentId": 166, "tags": {"name": "682.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9458, "timestamp": 38605350093, "id": 195, "parentId": 166, "tags": {"name": "240.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 9455, "timestamp": 38605350098, "id": 196, "parentId": 166, "tags": {"name": "535.js", "cache": "HIT"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 104, "timestamp": 38605359450, "id": 198, "parentId": 166, "tags": {"name": "162.js", "cache": "HIT"}, "startTime": 1753811411462, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 71, "timestamp": 38605359484, "id": 199, "parentId": 166, "tags": {"name": "203.js", "cache": "HIT"}, "startTime": 1753811411462, "traceId": "a85d0bbb16d5e901"}, {"name": "minify-js", "duration": 32627, "timestamp": 38605350103, "id": 197, "parentId": 166, "tags": {"name": "792.js", "cache": "MISS"}, "startTime": 1753811411453, "traceId": "a85d0bbb16d5e901"}, {"name": "terser-webpack-plugin-optimize", "duration": 47253, "timestamp": 38605335496, "id": 166, "parentId": 19, "tags": {"compilationName": "server", "swcMinify": true}, "startTime": 1753811411438, "traceId": "a85d0bbb16d5e901"}, {"name": "css-minimizer-plugin", "duration": 204, "timestamp": 38605382906, "id": 200, "parentId": 19, "tags": {}, "startTime": 1753811411486, "traceId": "a85d0bbb16d5e901"}, {"name": "create-trace-assets", "duration": 1875, "timestamp": 38605383362, "id": 201, "parentId": 20, "tags": {}, "startTime": 1753811411486, "traceId": "a85d0bbb16d5e901"}, {"name": "seal", "duration": 701583, "timestamp": 38604708808, "id": 153, "parentId": 19, "tags": {}, "startTime": 1753811410812, "traceId": "a85d0bbb16d5e901"}, {"name": "webpack-compilation", "duration": 3830685, "timestamp": 38601629125, "id": 19, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753811407732, "traceId": "a85d0bbb16d5e901"}, {"name": "emit", "duration": 208456, "timestamp": 38605460478, "id": 202, "parentId": 16, "tags": {}, "startTime": 1753811411563, "traceId": "a85d0bbb16d5e901"}, {"name": "webpack-close", "duration": 226441, "timestamp": 38605669903, "id": 203, "parentId": 16, "tags": {"name": "server"}, "startTime": 1753811411773, "traceId": "a85d0bbb16d5e901"}, {"name": "webpack-generate-error-stats", "duration": 3707, "timestamp": 38605896426, "id": 204, "parentId": 203, "tags": {}, "startTime": 1753811411999, "traceId": "a85d0bbb16d5e901"}, {"name": "run-webpack-compiler", "duration": 5358545, "timestamp": 38600541989, "id": 16, "parentId": 15, "tags": {}, "startTime": 1753811406645, "traceId": "a85d0bbb16d5e901"}, {"name": "format-webpack-messages", "duration": 103, "timestamp": 38605900544, "id": 205, "parentId": 15, "tags": {}, "startTime": 1753811412003, "traceId": "a85d0bbb16d5e901"}, {"name": "worker-main-server", "duration": 5359348, "timestamp": 38600541449, "id": 15, "parentId": 1, "tags": {}, "startTime": 1753811406644, "traceId": "a85d0bbb16d5e901"}, {"name": "create-entrypoints", "duration": 79379, "timestamp": 38607362700, "id": 208, "parentId": 206, "tags": {}, "startTime": 1753811413465, "traceId": "a85d0bbb16d5e901"}, {"name": "generate-webpack-config", "duration": 711300, "timestamp": 38607442535, "id": 209, "parentId": 207, "tags": {}, "startTime": 1753811413545, "traceId": "a85d0bbb16d5e901"}]