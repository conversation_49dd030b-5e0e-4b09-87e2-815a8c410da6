(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931,882],{88342:function(e,t,r){Promise.resolve().then(r.bind(r,55982))},55982:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return J}});var s=r(57437),a=r(2265),i=r(86595),l=r(91723),n=r(88997),o=r(70525),c=r(40875),d=r(47465),u=r(32489),h=r(18930),m=r(98011),g=r(59604),x=r(68661),p=r(60719),y=e=>{let{selectedCategory:t,onCategoryChange:r}=e,[i,n]=(0,a.useState)([]),[o,d]=(0,a.useState)(!0),[y,f]=(0,a.useState)(!1),[v,b]=(0,a.useState)([]);(0,a.useEffect)(()=>{(async()=>{try{let e=await m.petAPI.getCategories();e.success&&n(e.data)}catch(e){console.error("获取分类失败:",e)}finally{d(!1)}})(),b(g.Fd.getHistory())},[]);let j=(e,t)=>{r(e),e&&t&&(g.Fd.addItem(e,t),b(g.Fd.getHistory()))},w=(e,t)=>{r(e),f(!1),g.Fd.addItem(e,t),b(g.Fd.getHistory())},N=(e,t)=>{t.stopPropagation(),g.Fd.removeItem(e),b(g.Fd.getHistory())},C=()=>{g.Fd.clearHistory(),b([]),f(!1)},S=(0,p.O)(()=>{f(!1)});if(o)return(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:(0,s.jsx)("div",{className:"animate-pulse",children:(0,s.jsx)("div",{className:"flex space-x-4",children:[1,2,3,4,5,6,7,8,9].map(e=>(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"},e))})})})});let k=i.filter(e=>1===e.level).sort((e,t)=>e.order-t.order).map(e=>({...e,children:i.filter(t=>2===t.level&&t.parent_id===e.id).sort((e,t)=>e.order-t.order)}));return(0,s.jsx)("div",{className:"bg-white border-b border-gray-200",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,s.jsx)("div",{className:"md:hidden py-4",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)("select",{value:t,onChange:e=>{let t=i.find(t=>t.id===e.target.value);j(e.target.value,null==t?void 0:t.name)},className:"w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500",children:[(0,s.jsx)("option",{value:"",children:"全部分类"}),k.map(e=>(0,s.jsxs)("optgroup",{label:e.name,children:[(0,s.jsx)("option",{value:e.id,children:e.name}),e.children.map(t=>(0,s.jsxs)("option",{value:t.id,children:[e.name," > ",t.name]},t.id))]},e.id))]}),v.length>0&&(0,s.jsx)("button",{onClick:()=>f(!y),className:"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600",children:(0,s.jsx)(l.Z,{className:"h-4 w-4"})}),y&&v.length>0&&(0,s.jsx)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-40 overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"分类历史"}),(0,s.jsx)("button",{onClick:C,className:"text-xs text-gray-400 hover:text-gray-600",children:"清空"})]}),v.map((e,t)=>(0,s.jsxs)("button",{onClick:()=>w(e.value,e.label),className:"w-full px-2 py-1 text-left text-sm hover:bg-gray-50 rounded flex items-center justify-between group",children:[(0,s.jsx)("span",{children:e.label}),(0,s.jsx)("button",{onClick:t=>N(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600",children:(0,s.jsx)(u.Z,{className:"h-3 w-3"})})]},t))]})})]})}),(0,s.jsx)("div",{className:"hidden md:block py-4",children:(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 items-center",children:[(0,s.jsx)("button",{onClick:()=>j(""),className:(0,x.cn)("px-4 py-2 rounded-lg text-sm font-medium transition-colors",""===t?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"全部分类"}),k.map(e=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsxs)("button",{onClick:()=>j(e.id,e.name),className:(0,x.cn)("flex items-center space-x-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors",t===e.id?"bg-primary-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,s.jsx)("span",{children:e.name}),e.children.length>0&&(0,s.jsx)(c.Z,{className:"h-3 w-3"})]}),e.children.length>0&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-10",children:(0,s.jsx)("div",{className:"p-2",children:e.children.map(e=>(0,s.jsx)("button",{onClick:()=>j(e.id,e.name),className:(0,x.cn)("block w-full text-left px-3 py-2 text-sm rounded-md transition-colors",t===e.id?"bg-primary-50 text-primary-700":"text-gray-700 hover:bg-gray-100"),children:e.name},e.id))})})]},e.id)),v.length>0&&(0,s.jsxs)("div",{className:"relative",ref:S,children:[(0,s.jsxs)("button",{onClick:()=>f(!y),className:(0,x.cn)("px-3 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-1",y?"bg-blue-100 text-blue-700":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:[(0,s.jsx)(l.Z,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:"历史"})]}),y&&(0,s.jsx)("div",{className:"absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500 font-medium",children:"分类历史"}),(0,s.jsxs)("button",{onClick:C,className:"text-xs text-gray-400 hover:text-gray-600 flex items-center space-x-1",children:[(0,s.jsx)(h.Z,{className:"h-3 w-3"}),(0,s.jsx)("span",{children:"清空"})]})]}),v.map((e,t)=>(0,s.jsxs)("button",{onClick:()=>w(e.value,e.label),className:"w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none text-sm rounded-md group flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-gray-700",children:e.label}),(0,s.jsx)("button",{onClick:t=>N(e.value,t),className:"opacity-0 group-hover:opacity-100 text-gray-400 hover:text-gray-600 transition-opacity p-1",children:(0,s.jsx)(u.Z,{className:"h-3 w-3"})})]},t))]})})]})]})})]})})},f=r(28819),v=r(89841),b=r(31215),j=r(77165),w=r(96502);function N(e){let{positionId:t,className:r="",fallbackContent:i}=e,[l,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(0),[h,m]=(0,a.useState)(!0),[g,x]=(0,a.useState)(!1),[p,y]=(0,a.useState)(!0);(0,a.useEffect)(()=>{f()},[t]),(0,a.useEffect)(()=>{if(l.length>1&&(null==o?void 0:o.rotation_interval)&&o.rotation_interval>0){let e=setInterval(()=>{u(e=>(e+1)%l.length)},o.rotation_interval);return()=>clearInterval(e)}},[l,o]);let f=async()=>{try{y(!0);let e=v(t);c(e.position),n(e.ads||[])}catch(e){console.error("加载广告失败:",e)}finally{setTimeout(()=>y(!1),500)}},v=e=>({home_banner:{position:{position_id:"home_banner",name:"首页横幅广告",page:"home",location:"top",width:728,height:90,ad_type:"banner",max_ads:3,rotation_interval:5e3,status:"active"},ads:[{_id:"banner_ad_1",title:"优质宠物用品推荐",content:"为您的爱宠提供最好的生活用品，健康快乐每一天！",image_url:"",target_url:"#",ad_type:"banner",priority:1}]},home_feed:{position:{position_id:"home_feed",name:"首页信息流广告",page:"home",location:"feed",width:300,height:200,ad_type:"feed",max_ads:5,rotation_interval:0,status:"active"},ads:[{_id:"feed_ad_1",title:"专业宠物医院",content:"24小时宠物医疗服务，专业医师团队，让您的爱宠健康无忧。",image_url:"",target_url:"#",ad_type:"feed",priority:1},{_id:"feed_ad_2",title:"宠物美容服务",content:"专业宠物美容，让您的爱宠更加美丽动人。",image_url:"",target_url:"#",ad_type:"feed",priority:2}]}})[e]||{position:null,ads:[]},b=e=>{N(e._id),e.target_url&&window.open(e.target_url,"_blank")},N=async e=>{try{console.log("广告点击记录:",{ad_id:e,timestamp:new Date})}catch(e){console.error("记录广告点击失败:",e)}},C=()=>{x(!0),localStorage.setItem("ad_hidden_".concat(t),Date.now().toString())};if(p)return(0,s.jsx)("div",{className:"animate-pulse ".concat(r),children:(0,s.jsx)("div",{className:"bg-gray-200 rounded-lg h-20"})});if(!(()=>{if(!o||"active"!==o.status||!l||0===l.length||g)return!1;let e=localStorage.getItem("ad_hidden_".concat(t));if(e){let t=parseInt(e);if((Date.now()-t)/36e5<24)return!1}return!0})())return i?(0,s.jsx)("div",{className:r,children:i}):null;let S=l[d];return(0,s.jsx)("div",{className:r,children:(()=>{switch(null==o?void 0:o.ad_type){case"banner":return(0,s.jsxs)("div",{className:"relative bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg overflow-hidden",children:[(0,s.jsx)("button",{onClick:C,className:"absolute top-2 right-2 z-10 bg-white/80 hover:bg-white rounded-full p-1 transition-colors",title:"隐藏广告",children:(0,s.jsx)(j.Z,{className:"h-4 w-4 text-gray-600"})}),(0,s.jsxs)("div",{className:"flex items-center p-4 cursor-pointer hover:bg-black/5 transition-colors",onClick:()=>b(S),children:[S.image_url&&(0,s.jsx)("img",{src:S.image_url,alt:S.title,className:"w-16 h-16 rounded-lg object-cover mr-4"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"font-medium text-gray-900 mb-1",children:S.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:S.content})]}),(0,s.jsx)("div",{className:"ml-4",children:(0,s.jsx)("span",{className:"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded",children:"广告"})})]})]});case"feed":return(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-100",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-gray-100",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500 bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:"推广内容"}),(0,s.jsx)("button",{onClick:C,className:"text-gray-400 hover:text-gray-600 transition-colors",title:"隐藏此广告",children:(0,s.jsx)(w.Z,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"p-4 cursor-pointer hover:bg-gray-50 transition-colors",onClick:()=>b(S),children:[S.image_url&&(0,s.jsx)("img",{src:S.image_url,alt:S.title,className:"w-full h-40 object-cover rounded-lg mb-3"}),(0,s.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:S.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-3",children:S.content}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"text-xs text-gray-500",children:"点击了解更多"}),(0,s.jsx)("div",{className:"flex items-center space-x-2",children:l.length>1&&(0,s.jsx)("div",{className:"flex space-x-1",children:l.map((e,t)=>(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t===d?"bg-blue-500":"bg-gray-300")},t))})})]})]})]});case"popup":return(0,s.jsx)("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg max-w-sm w-full overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("button",{onClick:C,className:"absolute top-2 right-2 z-10 bg-black/20 hover:bg-black/40 text-white rounded-full p-1 transition-colors",children:(0,s.jsx)(j.Z,{className:"h-5 w-5"})}),S.image_url&&(0,s.jsx)("img",{src:S.image_url,alt:S.title,className:"w-full h-48 object-cover"})]}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-bold text-lg text-gray-900 mb-2",children:S.title}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:S.content}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>b(S),className:"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors",children:"了解更多"}),(0,s.jsx)("button",{onClick:C,className:"px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",children:"跳过"})]})]})]})});default:return null}})()})}var C=r(9356),S=r(80240),k=r(33882);class _{static getInstance(){return _.instance||(_.instance=new _),_.instance}async optimizedPostQuery(e){let t=Date.now(),s="posts_".concat(JSON.stringify(e));if(!1!==e.useCache){let e=k.dataCache.get(s);if(e)return this.recordQueryStats("optimizedPostQuery",Date.now()-t),e}try{let a=this.buildOptimizedQuery(e),{petAPI:i}=await Promise.resolve().then(r.bind(r,98011)),l=await i.getOptimizedPosts(a);return l.success&&!1!==e.useCache&&k.dataCache.set(s,l,12e4),this.recordQueryStats("optimizedPostQuery",Date.now()-t),l}catch(e){throw console.error("查询优化失败:",e),e}}buildOptimizedQuery(e){let t={action:"query",page:e.page||1,limit:Math.min(e.limit||10,20),includeUserInfo:e.includeUserInfo||!1,includeCategoryInfo:e.includeCategoryInfo||!1};return e.category&&(t.category=e.category),e.type&&(t.type=e.type),e.location&&(t.location=e.location),e.userId&&(t.userId=e.userId),e.sortBy&&(t.sortBy=e.sortBy),e.currentUserId&&(t.currentUserId=e.currentUserId),t}async batchQuery(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5,r=[];for(let s=0;s<e.length;s+=t){let a=e.slice(s,s+t);(await Promise.allSettled(a.map(e=>e()))).forEach(e=>{"fulfilled"===e.status?r.push(e.value):console.error("批量查询失败:",e.reason)})}return r}async preloadRelatedData(e){if(!e.length)return;let t=Array.from(new Set(e.map(e=>e.user_id||e.author_id).filter(Boolean))),r=Array.from(new Set(e.map(e=>e.category).filter(Boolean)));t.length>0&&this.preloadUsers(t),r.length>0&&this.preloadCategories(r)}async preloadUsers(e){e.filter(e=>!k.dataCache.get("user_".concat(e))).length}async preloadCategories(e){e.filter(e=>!k.dataCache.get("category_".concat(e))).length}recordQueryStats(e,t){let r=this.queryStats.get(e)||{count:0,avgTime:0};r.count++,r.avgTime=(r.avgTime*(r.count-1)+t)/r.count,this.queryStats.set(e,r)}getQueryStats(){return new Map(this.queryStats)}clearStats(){this.queryStats.clear()}constructor(){this.queryStats=new Map}}let I=_.getInstance();var T=r(98734);class E{generateKey(e,t){let{limit:r,...s}=e;return JSON.stringify({...s,page:t,limit:r})}get(e,t){let r=this.generateKey(e,t),s=this.cache.get(r);return s&&Date.now()-s.timestamp<this.TTL?s:null}set(e,t,r,s,a){let i=this.generateKey(e,t);this.cache.set(i,{data:r,timestamp:Date.now(),hasMore:s,totalCount:a})}clear(){this.cache.clear(),console.log("帖子缓存已清理")}clearByFilters(e){let t=[];this.cache.forEach((r,s)=>{try{let r=JSON.parse(s),a=!1;if(0===Object.keys(e).length)a=!0;else for(let[t,s]of Object.entries(e))if(r[t]===s){a=!0;break}a&&t.push(s)}catch(e){}}),t.forEach(e=>this.cache.delete(e)),console.log("清理了 ".concat(t.length," 个缓存项"))}async preloadNextPage(e,t,r){let s=t+1,a=this.generateKey(e,s);if(!this.cache.get(a))try{let t=await I.optimizedPostQuery({page:s,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0,currentUserId:r});if(t.success&&t.data){var i,l;let r=Array.isArray(t.data)?t.data:t.data.posts||[],a=(null===(i=t.pagination)||void 0===i?void 0:i.hasMore)||!1,n=(null===(l=t.pagination)||void 0===l?void 0:l.total)||r.length;this.set(e,s,r,a,n),console.log("预加载第".concat(s,"页成功，共").concat(r.length,"条数据"))}}catch(e){console.warn("预加载下一页失败:",e)}}constructor(){this.cache=new Map,this.TTL=3e4}}let O=new E,P=e=>{let{filters:t,enabled:r=!0,debounceMs:s=300}=e,{user:i}=(0,T.a)(),[l,n]=(0,a.useState)([]),[o,c]=(0,a.useState)(!1),[d,u]=(0,a.useState)(!1),[h,m]=(0,a.useState)(null),[g,x]=(0,a.useState)(!0),[p,y]=(0,a.useState)(0),f=(0,a.useRef)(null),v=(0,a.useRef)(null),b=(0,a.useRef)(t),j=(0,a.useCallback)((e,t)=>{if(!t.trim())return e;let r=t.toLowerCase().trim();return e.filter(e=>!!(e.breed&&e.breed.toLowerCase().includes(r)||e.title&&e.title.toLowerCase().includes(r)||e.tags&&e.tags.some(e=>e.toLowerCase().includes(r))))},[]),w=(0,a.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2?arguments[2]:void 0;try{let l;let o=t?e.page:1;if(!t){let t=O.get(e,o);if(t){n(t.data),x(t.hasMore),y(t.totalCount),m(null);return}}t?u(!0):c(!0);let d=Date.now();l=await I.optimizedPostQuery({page:o,limit:e.limit,sortBy:e.sortBy,category:e.category||void 0,type:"all"!==e.petType?e.petType:void 0,location:e.location||void 0,includeUserInfo:!0,includeCategoryInfo:!0,useCache:!0,currentUserId:null==i?void 0:i._id});let h=Date.now()-d;if(void 0!==S.Bm&&S.Bm.recordApiResponse("optimizedPostQuery",h),null==r?void 0:r.aborted)return;if(l.success){var s,a;let r=l.data||[];e.breed&&(r=j(r,e.breed)),t?n(e=>[...e,...r]):n(r);let c=(null===(s=l.pagination)||void 0===s?void 0:s.hasMore)||!1,d=(null===(a=l.pagination)||void 0===a?void 0:a.total)||r.length;x(c),y(d),m(null),!t&&(O.set(e,o,r,c,d),c&&1===o&&setTimeout(()=>{O.preloadNextPage(e,o,null==i?void 0:i._id)},1e3))}else throw Error(l.message||"获取数据失败")}catch(e){if(null==r?void 0:r.aborted)return;console.error("获取宠物列表失败:",e),m(e.message||"获取数据失败"),t||C.C.error("获取宠物列表失败，请重试")}finally{(null==r?void 0:r.aborted)||(c(!1),u(!1))}},[j]),N=(0,a.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(f.current&&f.current.abort(),v.current&&clearTimeout(v.current),t){let t=new AbortController;f.current=t,w(e,!0,t.signal);return}v.current=setTimeout(()=>{let t=new AbortController;f.current=t,w(e,!1,t.signal)},s)},[w,s]),k=(0,a.useCallback)(()=>{if(!d&&g&&r){let e={...b.current,page:b.current.page+1};b.current=e,N(e,!0)}},[d,g,r,N]),_=(0,a.useCallback)(()=>{if(O.clear(),n([]),m(null),x(!0),y(0),r){let e={...t,page:1};b.current=e,N(e)}},[t,r,N]);return(0,a.useEffect)(()=>{if(r)return(b.current.category!==t.category||b.current.petType!==t.petType||b.current.location!==t.location||b.current.breed!==t.breed||b.current.sortBy!==t.sortBy||1===t.page)&&(n([]),m(null),x(!0),y(0)),b.current=t,N(t),()=>{v.current&&clearTimeout(v.current)}},[t,r,N]),(0,a.useEffect)(()=>()=>{f.current&&f.current.abort(),v.current&&clearTimeout(v.current)},[]),{posts:l,loading:o,loadingMore:d,error:h,hasMore:g,loadMore:k,refresh:_,totalCount:p}};var D=e=>{let{filters:t}=e,{posts:r,loading:i,loadingMore:l,error:n,hasMore:o,loadMore:c,refresh:d,totalCount:u}=P({filters:t}),{ref:h,inView:m}=(0,f.YD)({threshold:0,rootMargin:"100px"});return((0,a.useEffect)(()=>{m&&o&&!i&&!l&&c()},[m,o,i,l,c]),n&&0===r.length)?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("div",{className:"text-gray-500 mb-4",children:[(0,s.jsx)("p",{className:"text-lg font-medium",children:"加载失败"}),(0,s.jsx)("p",{className:"text-sm",children:n})]}),(0,s.jsx)("button",{onClick:()=>{d()},className:"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors",children:"重试"})]}):i&&0===r.length?(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:Array.from({length:20}).map((e,t)=>(0,s.jsx)(b.gG,{},t))}):i||0!==r.length?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4",children:(()=>{let e=[];for(let t=0;t<r.length;t++)e.push((0,s.jsx)(v.Z,{post:r[t]},r[t]._id)),(t+1)%6==0&&t<r.length-1&&e.push((0,s.jsx)(N,{positionId:"home_feed",className:"col-span-2 md:col-span-1"},"ad-".concat(t)));return e})()}),o&&(0,s.jsx)("div",{ref:h,className:"flex justify-center py-8",children:l?(0,s.jsx)(b.gb,{size:"sm",text:"加载更多..."}):(0,s.jsx)("div",{className:"text-gray-400 text-sm",children:"滑动到底部加载更多"})}),!o&&r.length>0&&(0,s.jsx)("div",{className:"text-center py-8 text-gray-400 text-sm",children:"已显示全部内容"})]}):(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsxs)("div",{className:"text-gray-500",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC3E"}),(0,s.jsx)("p",{className:"text-lg font-medium mb-2",children:"暂无宠物信息"}),(0,s.jsx)("p",{className:"text-sm",children:t.category?"该分类下暂无宠物，试试其他分类吧":"还没有人发布宠物，快来成为第一个吧！"})]})})},L=r(56334),B=r(83774);let z="location_filter_history";var R=e=>{let{selectedLocation:t,onLocationChange:r}=e,[i,l]=(0,a.useState)(t),[n,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),u=()=>{try{let e=localStorage.getItem(z);return e?JSON.parse(e):[]}catch(e){return[]}},h=e=>{try{localStorage.setItem(z,JSON.stringify(e))}catch(e){}},m=e=>{if(!e.trim())return;let t=[e,...u().filter(t=>t!==e)].slice(0,10);o(t),h(t)};(0,a.useEffect)(()=>{o(u())},[]);let g=e=>{l(e),r(e),d(!1)},x=()=>{i&&i.trim()&&m(i.trim())},y=(0,p.O)(()=>{d(!1)});return(0,a.useEffect)(()=>{l(t)},[t]),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"flex-1 relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(B.Z,{className:"h-4 w-4 text-gray-400"})}),(0,s.jsx)("input",{type:"text",value:i,onChange:e=>{let t=e.target.value;l(t),r(t)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),x())},onBlur:x,placeholder:"输入地区筛选，如：北京、上海等",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})]}),(0,s.jsxs)("div",{className:"relative",ref:y,children:[(0,s.jsx)("button",{type:"button",onClick:()=>{d(!c)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看地区历史",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),c&&n.length>0&&(0,s.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,s.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"地区历史"]}),(0,s.jsx)("button",{onClick:()=>{o([]),h([]),d(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),(0,s.jsx)("div",{className:"max-h-48 overflow-y-auto",children:n.map((e,t)=>(0,s.jsxs)("div",{onClick:()=>g(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors flex items-center",children:[(0,s.jsx)(B.Z,{className:"h-3 w-3 text-gray-400 mr-2 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-sm",children:e})]},"".concat(e,"-").concat(t)))})]})]})]}),i&&(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>{l(""),r(""),d(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})};let M="breed_filter_history";var H=e=>{let{selectedBreed:t,onBreedChange:r}=e,[i,l]=(0,a.useState)(t),[n,o]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1),u=()=>{try{let e=localStorage.getItem(M);return e?JSON.parse(e):[]}catch(e){return[]}},h=e=>{try{localStorage.setItem(M,JSON.stringify(e))}catch(e){}},m=e=>{if(!e.trim())return;let t=[e,...u().filter(t=>t!==e)].slice(0,10);o(t),h(t)};(0,a.useEffect)(()=>{o(u())},[]);let g=e=>{l(e),r(e),d(!1)},x=()=>{i&&i.trim()&&m(i.trim())},y=(0,p.O)(()=>{d(!1)});return(0,a.useEffect)(()=>{l(t)},[t]),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("input",{type:"text",value:i,onChange:e=>{let t=e.target.value;l(t),r(t)},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),x())},onBlur:x,placeholder:"输入品种筛选，如：柴犬、英短等",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-sm"})}),(0,s.jsxs)("div",{className:"relative",ref:y,children:[(0,s.jsx)("button",{type:"button",onClick:()=>{d(!c)},className:"px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors",title:"查看品种历史",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),c&&n.length>0&&(0,s.jsxs)("div",{className:"absolute right-0 top-full mt-1 w-64 bg-white border border-gray-300 rounded-lg shadow-lg z-10",children:[(0,s.jsxs)("div",{className:"px-4 py-2 bg-gray-50 border-b border-gray-200 flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-600 flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"品种历史"]}),(0,s.jsx)("button",{onClick:()=>{o([]),h([]),d(!1)},className:"text-xs text-red-500 hover:text-red-700 transition-colors",children:"清除"})]}),(0,s.jsx)("div",{className:"max-h-48 overflow-y-auto",children:n.map((e,t)=>(0,s.jsx)("div",{onClick:()=>g(e),className:"px-4 py-2 cursor-pointer hover:bg-gray-50 transition-colors",children:(0,s.jsx)("span",{className:"text-sm",children:e})},"".concat(e,"-").concat(t)))})]})]})]}),i&&(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("button",{onClick:()=>{l(""),r(""),d(!1)},className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"清空筛选"})})]})},U=r(69076),Z=e=>{let{filters:t,onRemoveFilter:r,onClearAll:a,hasActiveFilters:i,totalCount:l}=e;if(!i)return null;let n=(e,t)=>{switch(e){case"category":return"分类: ".concat(t);case"petType":return"类型: ".concat({breeding:"配种",selling:"出售",lost:"寻回"}[t]||t);case"location":return"地区: ".concat(t);case"breed":return"品种: ".concat(t);case"sortBy":return"排序: ".concat({created_at:"最新发布",likes_count:"最多点赞",wants_count:"最想要",avg_rating:"最高评分",priority:"智能推荐"}[t]||t);default:return"".concat(e,": ").concat(t)}},o=Object.entries(t).filter(e=>{let[t,r]=e;return"page"!==t&&"limit"!==t&&("petType"!==t||"all"!==r)&&("sortBy"!==t||"priority"!==r)&&r&&""!==r});return(0,s.jsx)("div",{className:"bg-white border-b border-gray-200 px-4 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 flex-wrap",children:[(0,s.jsx)("span",{className:"text-sm text-gray-600 font-medium",children:"筛选条件:"}),o.map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"inline-flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full",children:[(0,s.jsx)("span",{children:n(t,a)}),(0,s.jsx)("button",{onClick:()=>r(t),className:"ml-1.5 inline-flex items-center justify-center w-3 h-3 text-blue-600 hover:text-blue-800 hover:bg-blue-200 rounded-full transition-colors","aria-label":"移除".concat(t,"筛选条件"),children:(0,s.jsx)(u.Z,{className:"w-2 h-2"})})]},t)}),void 0!==l&&(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["共找到 ",l," 个结果"]})]}),(0,s.jsxs)("button",{onClick:a,className:"inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors",children:[(0,s.jsx)(U.Z,{className:"w-4 h-4 mr-1"}),"清除全部"]})]})})},K=r(99376);let F={category:"",petType:"all",location:"",breed:"",sortBy:"priority",page:1,limit:20},A=()=>{let e=(0,K.useRouter)(),t=(0,K.useSearchParams)(),r=(0,a.useCallback)(()=>({category:t.get("category")||F.category,petType:t.get("petType")||F.petType,location:t.get("location")||F.location,breed:t.get("breed")||F.breed,sortBy:t.get("sortBy")||F.sortBy,page:parseInt(t.get("page")||"1"),limit:parseInt(t.get("limit")||"20")}),[t]),[s,i]=(0,a.useState)(r),l=(0,a.useCallback)(t=>{let r=new URLSearchParams;t.category&&t.category!==F.category&&r.set("category",t.category),t.petType!==F.petType&&r.set("petType",t.petType),t.location&&t.location!==F.location&&r.set("location",t.location),t.breed&&t.breed!==F.breed&&r.set("breed",t.breed),t.sortBy!==F.sortBy&&r.set("sortBy",t.sortBy),t.page!==F.page&&r.set("page",t.page.toString());let s=r.toString()?"?".concat(r.toString()):"/";e.replace(s,{scroll:!1})},[e]),n=(0,a.useCallback)((e,t)=>{i(r=>{let s={...r,[e]:t,page:"page"===e?t:1};return l(s),s})},[l]),o=(0,a.useCallback)(()=>{i(F),l(F)},[l]),c=(0,a.useCallback)(()=>{n("page",1)},[n]),d=(0,a.useCallback)(()=>{let e=new URLSearchParams;return Object.entries(s).forEach(t=>{let[r,s]=t;s&&s!==F[r]&&e.set(r,s.toString())}),e},[s]),u=(0,a.useCallback)(()=>Object.entries(s).some(e=>{let[t,r]=e;return"page"!==t&&"limit"!==t&&r!==F[t]}),[s]);return(0,a.useEffect)(()=>{i(r())},[t,r]),{filters:s,updateFilter:n,resetFilters:o,resetPage:c,getQueryParams:d,hasActiveFilters:u()}};var J=()=>{let{filters:e,updateFilter:t,resetFilters:r,hasActiveFilters:u}=A(),[h,m]=(0,a.useState)(!1),[g,p]=(0,a.useState)(!1),f=[{value:"priority",label:"智能推荐",icon:i.Z},{value:"created_at",label:"最新发布",icon:l.Z},{value:"likes_count",label:"最多点赞",icon:n.Z},{value:"wants_count",label:"最想要",icon:o.Z},{value:"avg_rating",label:"最高评分",icon:i.Z}],v=[{value:"all",label:"全部",color:"bg-gray-500"},{value:"breeding",label:"配种",color:"bg-pink-500"},{value:"selling",label:"出售",color:"bg-green-500"},{value:"lost",label:"寻回",color:"bg-orange-500"}],b=f.find(t=>t.value===e.sortBy),j=v.find(t=>t.value===e.petType);return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(d.Z,{}),(0,s.jsx)(y,{selectedCategory:e.category,onCategoryChange:e=>t("category",e)}),(0,s.jsx)(Z,{filters:e,onRemoveFilter:e=>{switch(e){case"category":t("category","");break;case"petType":t("petType","all");break;case"location":t("location","");break;case"breed":t("breed","");break;case"sortBy":t("sortBy","priority")}},onClearAll:r,hasActiveFilters:u}),(0,s.jsxs)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"发现宠物"})}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(L.Z,{variant:"outline",onClick:()=>m(!h),className:"flex items-center space-x-2",children:[b&&(0,s.jsx)(b.icon,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:(null==b?void 0:b.label)||"排序"}),(0,s.jsx)(c.Z,{className:"w-4 h-4"})]}),h&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:(0,s.jsx)("div",{className:"py-1",children:f.map(r=>(0,s.jsxs)("button",{onClick:()=>{t("sortBy",r.value),m(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.sortBy===r.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[(0,s.jsx)(r.icon,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:r.label})]},r.value))})})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsxs)(L.Z,{variant:"outline",onClick:()=>p(!g),className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:(0,x.cn)("w-3 h-3 rounded-full",null==j?void 0:j.color)}),(0,s.jsx)("span",{children:(null==j?void 0:j.label)||"类型"}),(0,s.jsx)(c.Z,{className:"w-4 h-4"})]}),g&&(0,s.jsx)("div",{className:"absolute top-full left-0 mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 z-10",children:(0,s.jsx)("div",{className:"py-1",children:v.map(r=>(0,s.jsxs)("button",{onClick:()=>{t("petType",r.value),p(!1)},className:(0,x.cn)("w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50",e.petType===r.value?"bg-primary-50 text-primary-600":"text-gray-700"),children:[(0,s.jsx)("div",{className:(0,x.cn)("w-3 h-3 rounded-full",r.color)}),(0,s.jsx)("span",{children:r.label})]},r.value))})})]}),(0,s.jsx)("div",{className:"flex-1 min-w-[200px]",children:(0,s.jsx)(R,{selectedLocation:e.location,onLocationChange:e=>t("location",e)})}),(0,s.jsx)("div",{className:"flex-1 min-w-[200px]",children:(0,s.jsx)(H,{selectedBreed:e.breed,onBreedChange:e=>t("breed",e)})})]})]}),(0,s.jsx)(N,{positionId:"home_banner",className:"mb-6",fallbackContent:(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 text-center",children:(0,s.jsx)("p",{className:"text-gray-600",children:"\uD83D\uDC3E 发现更多可爱的宠物朋友"})})}),(0,s.jsx)(D,{filters:e})]})]})}},33882:function(e,t,r){"use strict";r.d(t,{U_:function(){return i},dataCache:function(){return l}});class s{set(e,t,r){let s=Date.now(),a=r||this.config.ttl;this.cache.size>=this.config.maxSize&&this.evict(),this.cache.set(e,{data:t,timestamp:s,ttl:a,accessCount:0,lastAccess:s})}get(e){let t=this.cache.get(e);if(!t)return null;let r=Date.now();return r-t.timestamp>t.ttl?(this.cache.delete(e),null):(t.accessCount++,t.lastAccess=r,t.data)}delete(e){return this.cache.delete(e)}clear(){this.cache.clear()}evict(){let e;if(0!==this.cache.size){switch(this.config.strategy){case"LRU":e=this.findLRUKey();break;case"FIFO":e=this.findFIFOKey();break;case"TTL":e=this.findExpiredKey();break;default:e=this.cache.keys().next().value}e&&this.cache.delete(e)}}findLRUKey(){let e;let t=Date.now();return this.cache.forEach((r,s)=>{r.lastAccess<t&&(t=r.lastAccess,e=s)}),e}findFIFOKey(){let e;let t=Date.now();return this.cache.forEach((r,s)=>{r.timestamp<t&&(t=r.timestamp,e=s)}),e}findExpiredKey(){let e;let t=Date.now();return(this.cache.forEach((r,s)=>{if(t-r.timestamp>r.ttl){e=s;return}}),e)?e:this.findLRUKey()}getStats(){let e=Date.now(),t=0,r=0;return this.cache.forEach(s=>{e-s.timestamp>s.ttl&&t++,r+=JSON.stringify(s.data).length}),{size:this.cache.size,maxSize:this.config.maxSize,expired:t,totalSize:r,hitRate:this.calculateHitRate()}}calculateHitRate(){let e=0;return this.cache.forEach(t=>{e+=t.accessCount}),e>0?this.cache.size/e*100:0}constructor(e={}){this.cache=new Map,this.config={ttl:3e5,maxSize:100,strategy:"LRU",...e}}}class a{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:864e5;if(!this.storage)return;let s={data:t,timestamp:Date.now(),ttl:r};try{this.storage.setItem(this.prefix+e,JSON.stringify(s))}catch(t){console.warn("浏览器存储空间不足，清理过期缓存"),this.cleanup();try{this.storage.setItem(this.prefix+e,JSON.stringify(s))}catch(e){console.error("缓存设置失败:",e)}}}get(e){if(!this.storage)return null;try{let t=this.storage.getItem(this.prefix+e);if(!t)return null;let r=JSON.parse(t);if(Date.now()-r.timestamp>r.ttl)return this.storage.removeItem(this.prefix+e),null;return r.data}catch(e){return console.error("缓存读取失败:",e),null}}delete(e){this.storage&&this.storage.removeItem(this.prefix+e)}cleanup(){if(!this.storage)return;let e=Date.now(),t=[];for(let r=0;r<this.storage.length;r++){let s=this.storage.key(r);if(s&&s.startsWith(this.prefix))try{let r=this.storage.getItem(s);if(r){let a=JSON.parse(r);e-a.timestamp>a.ttl&&t.push(s)}}catch(e){t.push(s)}}t.forEach(e=>this.storage.removeItem(e))}clear(){if(!this.storage)return;let e=[];for(let t=0;t<this.storage.length;t++){let r=this.storage.key(t);r&&r.startsWith(this.prefix)&&e.push(r)}e.forEach(e=>this.storage.removeItem(e))}constructor(e="pet_cache_",t=!1){this.prefix=e,this.storage=t?sessionStorage:localStorage}}let i=new s({ttl:18e5,maxSize:50,strategy:"LRU"}),l=new s({ttl:3e5,maxSize:100,strategy:"LRU"});new a("pet_app_")},59604:function(e,t,r){"use strict";r.d(t,{Fd:function(){return i},KX:function(){return l}});class s{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e)return JSON.parse(e).sort((e,t)=>t.timestamp-e.timestamp)}catch(e){console.error("获取历史记录失败:",e)}return[]}addItem(e,t){if(!e.trim())return;let r={value:e.trim(),label:t||e.trim(),timestamp:Date.now()},s=this.getHistory();(s=s.filter(e=>e.value!==r.value)).unshift(r),s.length>this.maxItems&&(s=s.slice(0,this.maxItems)),this.saveHistory(s)}removeItem(e){let t=this.getHistory().filter(t=>t.value!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getHistoryValues(){return this.getHistory().map(e=>e.value)}hasItem(e){return this.getHistoryValues().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存历史记录失败:",e)}}constructor(e,t=10){this.storageKey=e,this.maxItems=t}}class a{getHistory(){try{let e=localStorage.getItem(this.storageKey);if(e){let t=JSON.parse(e),r=Date.now()-864e5*this.maxDays,s=t.filter(e=>e.timestamp>r);return s.length!==t.length&&this.saveHistory(s),s.sort((e,t)=>t.timestamp-e.timestamp)}}catch(e){console.error("获取浏览历史记录失败:",e)}return[]}addBrowseRecord(e,t,r,s,a){if(!e||!t)return;let i={postId:e,title:t.trim(),author:r.trim(),authorId:s,image:a,timestamp:Date.now()},l=this.getHistory();(l=l.filter(e=>e.postId!==i.postId)).unshift(i),l.length>this.maxItems&&(l=l.slice(0,this.maxItems)),this.saveHistory(l)}removeRecord(e){let t=this.getHistory().filter(t=>t.postId!==e);this.saveHistory(t)}clearHistory(){localStorage.removeItem(this.storageKey)}getRecentPostIds(){return this.getHistory().map(e=>e.postId)}hasBrowsed(e){return this.getRecentPostIds().includes(e)}saveHistory(e){try{localStorage.setItem(this.storageKey,JSON.stringify(e))}catch(e){console.error("保存浏览历史记录失败:",e)}}constructor(){this.storageKey="browse_history",this.maxDays=3,this.maxItems=50}}new s("location_history",10);let i=new s("category_history",8),l=new a},80240:function(e,t,r){"use strict";r.d(t,{Bm:function(){return a}});class s{static getInstance(){return s.instance||(s.instance=new s),s.instance}initializeObservers(){try{this.observeNavigation(),this.observePaint(),this.observeLayoutShift(),this.observeFirstInputDelay(),this.observeResources()}catch(e){console.warn("性能监控初始化失败:",e)}}observeNavigation(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"navigation"===e.entryType&&(this.metrics.pageLoadTime=e.loadEventEnd-e.fetchStart)})});e.observe({entryTypes:["navigation"]}),this.observers.push(e)}}observePaint(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{"first-contentful-paint"===e.name&&(this.metrics.firstContentfulPaint=e.startTime)})});e.observe({entryTypes:["paint"]}),this.observers.push(e)}if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let t=e.getEntries(),r=t[t.length-1];this.metrics.largestContentfulPaint=r.startTime});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}}observeLayoutShift(){if("PerformanceObserver"in window){let e=0,t=new PerformanceObserver(t=>{t.getEntries().forEach(t=>{t.hadRecentInput||(e+=t.value,this.metrics.cumulativeLayoutShift=e)})});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}}observeFirstInputDelay(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{this.metrics.firstInputDelay=e.processingStart-e.startTime})});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}}observeResources(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{if("img"===e.initiatorType){let t=e.responseEnd-e.startTime;this.metrics.imageLoadTimes.push(t)}})});e.observe({entryTypes:["resource"]}),this.observers.push(e)}}recordApiResponse(e,t){this.metrics.apiResponseTimes.has(e)||this.metrics.apiResponseTimes.set(e,[]),this.metrics.apiResponseTimes.get(e).push(t)}recordImageLoad(e){this.metrics.imageLoadTimes.push(e)}getMemoryUsage(){return"memory"in performance?performance.memory:null}getMetrics(){return{...this.metrics,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceReport(){let e={};this.metrics.apiResponseTimes.forEach((t,r)=>{let s=t.reduce((e,t)=>e+t,0)/t.length;e[r]={avgResponseTime:Math.round(s),callCount:t.length,maxResponseTime:Math.round(Math.max(...t)),minResponseTime:Math.round(Math.min(...t))}});let t=this.metrics.imageLoadTimes.length>0?this.metrics.imageLoadTimes.reduce((e,t)=>e+t,0)/this.metrics.imageLoadTimes.length:0;return{coreWebVitals:{lcp:Math.round(this.metrics.largestContentfulPaint),fid:Math.round(this.metrics.firstInputDelay),cls:Math.round(1e3*this.metrics.cumulativeLayoutShift)/1e3},loadingPerformance:{pageLoadTime:Math.round(this.metrics.pageLoadTime),fcp:Math.round(this.metrics.firstContentfulPaint),avgImageLoadTime:Math.round(t)},apiPerformance:e,memoryUsage:this.getMemoryUsage()||void 0}}getPerformanceScore(){let e=this.metrics.largestContentfulPaint<=2500?100:this.metrics.largestContentfulPaint<=4e3?50:0,t=this.metrics.firstInputDelay<=100?100:this.metrics.firstInputDelay<=300?50:0,r=this.metrics.cumulativeLayoutShift<=.1?100:this.metrics.cumulativeLayoutShift<=.25?50:0;return{overall:Math.round((e+t+r)/3),breakdown:{loading:e,interactivity:t,visualStability:r}}}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[]}exportData(){return JSON.stringify({timestamp:new Date().toISOString(),url:window.location.href,userAgent:navigator.userAgent,metrics:this.getMetrics(),report:this.getPerformanceReport(),score:this.getPerformanceScore()},null,2)}constructor(){this.observers=[],this.metrics={pageLoadTime:0,firstContentfulPaint:0,largestContentfulPaint:0,firstInputDelay:0,cumulativeLayoutShift:0,imageLoadTimes:[],apiResponseTimes:new Map},this.initializeObservers()}}let a=s.getInstance()}},function(e){e.O(0,[649,19,347,554,721,319,44,11,734,825,465,971,117,744],function(){return e(e.s=88342)}),_N_E=e.O()}]);