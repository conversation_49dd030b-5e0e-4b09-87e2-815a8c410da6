import React from 'react';
import Link from 'next/link';
import { Heart, MapPin, X, User } from 'lucide-react';
import { Post } from '@/types';
import { formatNumber } from '@/utils';
import { formatAddressForDisplay } from '@/utils/addressUtils';
import { cn } from '@/utils';
import LazyImage from '@/components/ui/LazyImage';

interface PetCardProps {
  post: Post;
  className?: string;
  showRemoveFromFavorites?: boolean;
  onRemoveFromFavorites?: () => void;
  isDraft?: boolean;
}

const PetCard: React.FC<PetCardProps> = ({
  post,
  className,
  showRemoveFromFavorites = false,
  onRemoveFromFavorites,
  isDraft = false
}) => {
  // 获取第一张图片作为封面
  const coverImage = post.images?.[0] || 'https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center';
  


  // 获取宠物类型标签配置
  const getTypeConfig = (type?: string) => {
    switch (type) {
      case 'breeding':
        return { label: '配种', color: 'bg-pink-500' };
      case 'selling':
        return { label: '出售', color: 'bg-green-500' };
      case 'lost':
        return { label: '寻回', color: 'bg-orange-500' };
      case 'wanted':
        return { label: '求购', color: 'bg-purple-500' };
      default:
        return null;
    }
  };

  const typeConfig = getTypeConfig(post.type);

  return (
    <Link href={`/post/detail?id=${post._id}`}>
      <div className={cn(
        'bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer h-full flex flex-col',
        className
      )}>
        {/* 图片区域 - 优化高度比例，改善图片显示效果 */}
        <div className="relative w-full overflow-hidden" style={{ height: '200px' }}>
          <LazyImage
            src={coverImage}
            alt={post.breed || '宠物图片'}
            className="w-full h-full object-cover rounded-t-lg"
            fallback="https://images.unsplash.com/photo-1601758228041-f3b2795255f1?w=400&h=300&fit=crop&crop=center"
            quality={80}
            sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 20vw"
          />

          {/* 草稿标签 */}
          {isDraft && (
            <div className="absolute top-2 left-2 bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
              待发布
            </div>
          )}

          {/* 宠物类型标签 */}
          {!isDraft && typeConfig && (
            <div className={cn(
              "absolute top-2 left-2 text-white text-xs px-2 py-1 rounded-full font-medium",
              typeConfig.color
            )}>
              {typeConfig.label}
            </div>
          )}

          {/* 图片数量指示器 */}
          {post.images && post.images.length > 1 && (
            <div className="absolute top-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full">
              1/{post.images.length}
            </div>
          )}

          {/* 移除收藏按钮 */}
          {showRemoveFromFavorites && (
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onRemoveFromFavorites?.();
              }}
              className="absolute bottom-2 right-2 bg-red-500 hover:bg-red-600 text-white p-1.5 rounded-full transition-colors"
              title="从收藏中移除"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </div>

        {/* 信息区域 - 优化高度，左右两列布局 */}
        <div className="p-3 flex-1 flex flex-col justify-center" style={{ minHeight: '72px' }}>
          <div className="flex items-center justify-between h-full">
            {/* 左列：品种名称 + 作者昵称 */}
            <div className="flex flex-col justify-center flex-1 min-w-0 pr-2">
              {/* 品种名称（作为链接） */}
              <h3 className="font-medium text-gray-900 text-sm leading-tight truncate mb-1">
                {post.breed || '未知品种'}
              </h3>

              {/* 作者昵称 */}
              <div className="flex items-center space-x-1 text-xs text-gray-600">
                <User className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">
                  {post.user?.nickname || post.author?.nickname || '匿名用户'}
                </span>
              </div>
            </div>

            {/* 右列：位置 + 点赞数 */}
            <div className="flex flex-col justify-center items-end flex-shrink-0">
              {/* 发布位置 */}
              {post.location && (
                <div className="flex items-center space-x-1 text-xs text-gray-500 mb-1">
                  <MapPin className="h-3 w-3" />
                  <span className="truncate max-w-20" title={post.location}>
                    {formatAddressForDisplay(post.location)}
                  </span>
                </div>
              )}

              {/* 点赞数 */}
              <div className="flex items-center space-x-1 text-xs text-gray-500">
                <Heart className="h-3 w-3" />
                <span>{formatNumber(post.likes_count || 0)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PetCard;
