'use client';

import React, { useState } from 'react';
import { Filter, TrendingUp, Clock, Heart, Star, ChevronDown } from 'lucide-react';
import Header from '@/components/layout/Header';
import CategoryNav from '@/components/home/<USER>';
import PetGrid from '@/components/home/<USER>';
import Button from '@/components/ui/Button';
import SimpleLocationFilter from '@/components/filters/SimpleLocationFilter';
import BreedFilter from '@/components/filters/BreedFilter';
import FilterStatusBar from '@/components/filters/FilterStatusBar';
import AdContainer from '@/components/AdContainer';
import { cn } from '@/utils';
import { usePostFilters } from '@/hooks/usePostFilters';

type SortOption = 'created_at' | 'likes_count' | 'wants_count' | 'avg_rating' | 'priority';
type PetType = 'all' | 'breeding' | 'selling' | 'lost' | 'wanted';

const HomePage: React.FC = () => {
  // 使用筛选Hook
  const { filters, updateFilter, resetFilters, hasActiveFilters } = usePostFilters();

  // UI状态
  const [showSortMenu, setShowSortMenu] = useState(false);
  const [showTypeMenu, setShowTypeMenu] = useState(false);




  const sortOptions = [
    { value: 'priority', label: '智能推荐', icon: Star },
    { value: 'created_at', label: '最新发布', icon: Clock },
    { value: 'likes_count', label: '最多点赞', icon: Heart },
    { value: 'wants_count', label: '最想要', icon: TrendingUp },
    { value: 'avg_rating', label: '最高评分', icon: Star },
  ];

  const petTypeOptions = [
    { value: 'all', label: '全部', color: 'bg-gray-500' },
    { value: 'breeding', label: '配种', color: 'bg-pink-500' },
    { value: 'selling', label: '出售', color: 'bg-green-500' },
    { value: 'lost', label: '寻回', color: 'bg-orange-500' },
    { value: 'wanted', label: '求购', color: 'bg-purple-500' },
  ];

  const currentSortOption = sortOptions.find(option => option.value === filters.sortBy);
  const currentPetType = petTypeOptions.find(option => option.value === filters.petType);

  // 筛选器移除处理
  const handleRemoveFilter = (key: keyof typeof filters) => {
    switch (key) {
      case 'category':
        updateFilter('category', '');
        break;
      case 'petType':
        updateFilter('petType', 'all');
        break;
      case 'location':
        updateFilter('location', '');
        break;
      case 'breed':
        updateFilter('breed', '');
        break;
      case 'sortBy':
        updateFilter('sortBy', 'priority');
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <CategoryNav
        selectedCategory={filters.category}
        onCategoryChange={(category) => updateFilter('category', category)}
      />

      {/* 筛选状态栏 */}
      <FilterStatusBar
        filters={filters}
        onRemoveFilter={handleRemoveFilter}
        onClearAll={resetFilters}
        hasActiveFilters={hasActiveFilters}
      />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="space-y-4 mb-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">
              发现宠物
            </h1>
          </div>
          <div className="flex flex-wrap gap-3">
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2"
              >
                {currentSortOption && (
                  <currentSortOption.icon className="w-4 h-4" />
                )}
                <span>{currentSortOption?.label || '排序'}</span>
                <ChevronDown className="w-4 h-4" />
              </Button>
              {showSortMenu && (
                <div className="absolute top-full left-0 mt-1 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    {sortOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => {
                          updateFilter('sortBy', option.value as SortOption);
                          setShowSortMenu(false);
                        }}
                        className={cn(
                          'w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50',
                          filters.sortBy === option.value ? 'bg-primary-50 text-primary-600' : 'text-gray-700'
                        )}
                      >
                        <option.icon className="w-4 h-4" />
                        <span>{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
            <div className="relative">
              <Button
                variant="outline"
                onClick={() => setShowTypeMenu(!showTypeMenu)}
                className="flex items-center space-x-2"
              >
                <div className={cn('w-3 h-3 rounded-full', currentPetType?.color)} />
                <span>{currentPetType?.label || '类型'}</span>
                <ChevronDown className="w-4 h-4" />
              </Button>
              {showTypeMenu && (
                <div className="absolute top-full left-0 mt-1 w-32 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <div className="py-1">
                    {petTypeOptions.map((option) => (
                      <button
                        key={option.value}
                        onClick={() => {
                          updateFilter('petType', option.value as PetType);
                          setShowTypeMenu(false);
                        }}
                        className={cn(
                          'w-full px-4 py-2 text-left flex items-center space-x-2 hover:bg-gray-50',
                          filters.petType === option.value ? 'bg-primary-50 text-primary-600' : 'text-gray-700'
                        )}
                      >
                        <div className={cn('w-3 h-3 rounded-full', option.color)} />
                        <span>{option.label}</span>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 地理位置筛选 - 简化版 */}
            <div className="flex-1 min-w-[200px]">
              <SimpleLocationFilter
                selectedLocation={filters.location}
                onLocationChange={(location) => updateFilter('location', location)}
              />
            </div>

            {/* 品种筛选 - 新增 */}
            <div className="flex-1 min-w-[200px]">
              <BreedFilter
                selectedBreed={filters.breed}
                onBreedChange={(breed) => updateFilter('breed', breed)}
              />
            </div>
          </div>
        </div>

        {/* 首页横幅广告 */}
        <AdContainer
          positionId="home_banner"
          className="mb-6"
          fallbackContent={
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 text-center">
              <p className="text-gray-600">🐾 发现更多可爱的宠物朋友</p>
            </div>
          }
        />

        <PetGrid filters={filters} />
      </main>
    </div>
  );
};

export default HomePage;
