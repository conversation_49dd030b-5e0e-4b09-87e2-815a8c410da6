import React from 'react';
import { Modal, ModalBody } from './Modal';
import Button from './Button';
import { AlertTriangle, CheckCircle, Info } from 'lucide-react';

interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'danger' | 'warning' | 'info' | 'success';
  loading?: boolean;
  showIcon?: boolean; // 新增：是否显示图标
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = '确认',
  cancelText = '取消',
  type = 'info',
  loading = false,
  showIcon = true // 默认显示图标
}) => {
  const getIcon = () => {
    switch (type) {
      case 'danger':
        return <AlertTriangle className="w-6 h-6 text-red-600" />;
      case 'warning':
        return <AlertTriangle className="w-6 h-6 text-yellow-600" />;
      case 'success':
        return <CheckCircle className="w-6 h-6 text-green-600" />;
      default:
        return <Info className="w-6 h-6 text-blue-600" />;
    }
  };

  const getButtonVariant = () => {
    switch (type) {
      case 'danger':
        return 'danger';
      case 'warning':
        return 'warning';
      case 'success':
        return 'primary';
      default:
        return 'primary';
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title=""
      size="sm"
      showCloseButton={false}
    >
      <ModalBody>
        <div className="text-center">
          {/* 图标 - 根据 showIcon 属性决定是否显示 */}
          {showIcon && (
            <div className="mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
              {getIcon()}
            </div>
          )}

          {/* 标题 */}
          <h3 className={`text-lg font-medium text-gray-900 mb-2 ${!showIcon ? 'mt-2' : ''}`}>
            {title}
          </h3>

          {/* 消息 */}
          <p className="text-sm text-gray-500 mb-6">
            {message}
          </p>

          {/* 按钮 */}
          <div className="flex space-x-3 justify-center">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="min-w-[80px]"
            >
              {cancelText}
            </Button>
            <Button
              variant={getButtonVariant()}
              onClick={onConfirm}
              loading={loading}
              disabled={loading}
              className="min-w-[80px]"
            >
              {confirmText}
            </Button>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default ConfirmDialog;
