(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[601],{8775:function(e,t,s){Promise.resolve().then(s.bind(s,98426))},98426:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return J}});var a=s(57437),r=s(2265),i=s(99376),n=s(32660),l=s(87769),c=s(42208),o=s(88997),d=s(37157),u=s(86595),x=s(82718),m=s(56266),h=s(68919),g=s(46211),f=s(92369),v=s(91723),p=s(83774),y=s(19764),j=s(10476),b=s(68661),w=s(9356),N=s(98011),_=s(98702),C=s(56334);let k={1:"普通",2:"极品",3:"神品",4:"仙品",5:"圣品"},R={1:"违法违规图片（血腥暴力色情等）",2:"图片模糊看不清"};var S=e=>{var t,s,i,n,l,c,o,d,x,m,h,g,f,v,p,y;let{isOpen:j,onClose:R,post:S,onSuccess:Z}=e,[I,P]=(0,r.useState)(0),[O,z]=(0,r.useState)(!1);(0,r.useEffect)(()=>{var e,t;(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)&&P(S.user_interactions.userRating)},[S.user_interactions]);let E=()=>{var e,t;(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated)&&(null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)?P(S.user_interactions.userRating):P(0),z(!1)},A=()=>{E(),R()},J=async()=>{var e;if(0===I){w.C.warning("请选择评分");return}if(null===(e=S.user_interactions)||void 0===e?void 0:e.hasRated){w.C.warning("您已经评分过了");return}try{z(!0);let e=await N.petAPI.ratePet({postId:S._id,rating:I});if(e.success){w.C.success("评分成功！");try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(S._id)||(e.unshift(S._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("保存评分记录到本地存储失败:",e)}Z()}else w.C.error(e.message||"评分失败")}catch(e){w.C.error(e.message||"评分失败")}finally{z(!1)}};return(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)?(0,a.jsx)(_.u_,{isOpen:j,onClose:A,title:"评分统计",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900 mb-2",children:S.avg_rating.toFixed(1)}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-1 mb-2",children:[1,2,3,4,5].map(e=>(0,a.jsx)(u.Z,{className:(0,b.cn)("h-6 w-6",e<=Math.round(S.avg_rating)?"text-yellow-500 fill-current":"text-gray-300")},e))}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["基于 ",S.ratings_count," 个评分"]})]}),(0,a.jsx)("div",{className:"space-y-2",children:[5,4,3,2,1].map(e=>{var t;let s=(null===(t=S.rating_stats)||void 0===t?void 0:t[e])||0,r=S.ratings_count>0?s/S.ratings_count*100:0;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1 w-16",children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:e}),(0,a.jsx)(u.Z,{className:"h-3 w-3 text-yellow-500 fill-current"})]}),(0,a.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-yellow-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(r,"%")}})}),(0,a.jsx)("span",{className:"text-xs text-gray-500 w-8",children:s})]},e)})}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:"您的评分"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(e=>{var t;return(0,a.jsx)(u.Z,{className:(0,b.cn)("h-5 w-5",e<=((null===(t=S.user_interactions)||void 0===t?void 0:t.userRating)||0)?"text-yellow-500 fill-current":"text-gray-300")},e)})}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:k[null===(y=S.user_interactions)||void 0===y?void 0:y.userRating]})]})]})]})})}):(0,a.jsx)(_.u_,{isOpen:j,onClose:A,title:"为这只宠物评分",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:S.title})}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"点击星星为这只宠物评分"}),(0,a.jsx)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[1,2,3,4,5].map(e=>{var t,s;return(0,a.jsx)("button",{onClick:()=>{var t;(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)||P(e)},className:(0,b.cn)("transition-transform duration-200 focus:outline-none",(null===(t=S.user_interactions)||void 0===t?void 0:t.hasRated)?"cursor-not-allowed":"hover:scale-105 active:scale-95 cursor-pointer"),disabled:O||(null===(s=S.user_interactions)||void 0===s?void 0:s.hasRated),children:(0,a.jsx)(u.Z,{className:(0,b.cn)("h-8 w-8 transition-colors duration-200",e<=I?"text-yellow-500 fill-current":"text-gray-300")})},e)})}),I>0&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:k[I]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:[(null===(s=S.user_interactions)||void 0===s?void 0:s.hasRated)?"您的评价：":"",I," 星评分"]})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:(null===(i=S.user_interactions)||void 0===i?void 0:i.hasRated)?"评分统计":"评分标准"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐ 普通"}),(null===(n=S.user_interactions)||void 0===n?void 0:n.hasRated)&&(0,a.jsxs)("span",{children:[(null===(l=S.rating_stats)||void 0===l?void 0:l[1])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐ 极品"}),(null===(c=S.user_interactions)||void 0===c?void 0:c.hasRated)&&(0,a.jsxs)("span",{children:[(null===(o=S.rating_stats)||void 0===o?void 0:o[2])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐ 神品"}),(null===(d=S.user_interactions)||void 0===d?void 0:d.hasRated)&&(0,a.jsxs)("span",{children:[(null===(x=S.rating_stats)||void 0===x?void 0:x[3])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐ 仙品"}),(null===(m=S.user_interactions)||void 0===m?void 0:m.hasRated)&&(0,a.jsxs)("span",{children:[(null===(h=S.rating_stats)||void 0===h?void 0:h[4])||0,"人"]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"⭐⭐⭐⭐⭐ 圣品"}),(null===(g=S.user_interactions)||void 0===g?void 0:g.hasRated)&&(0,a.jsxs)("span",{children:[(null===(f=S.rating_stats)||void 0===f?void 0:f[5])||0,"人"]})]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(C.Z,{variant:"outline",onClick:A,className:"flex-1",disabled:O,children:(null===(v=S.user_interactions)||void 0===v?void 0:v.hasRated)?"关闭":"取消"}),!(null===(p=S.user_interactions)||void 0===p?void 0:p.hasRated)&&(0,a.jsx)(C.Z,{onClick:J,loading:O,className:"flex-1",disabled:0===I,children:"提交评分"})]})]})})})},Z=e=>{let{isOpen:t,onClose:s,postId:i,onSuccess:n}=e,[l,c]=(0,r.useState)(0),[o,d]=(0,r.useState)(!1),u=()=>{c(0),d(!1)},x=()=>{u(),s()},m=async()=>{if(0===l){w.C.warning("请选择举报原因");return}try{d(!0);let e=await N.petAPI.reportPost({postId:i,reason:R[l]});e.success?n():w.C.error(e.message||"举报失败")}catch(e){w.C.error(e.message||"举报失败")}finally{d(!1)}},h=Object.entries(R).map(e=>{let[t,s]=e;return{id:parseInt(t),label:s}});return(0,a.jsx)(_.u_,{isOpen:t,onClose:x,title:"举报帖子",size:"sm",children:(0,a.jsx)(_.fe,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-sm text-yellow-800",children:"请选择举报原因，我们会认真处理每一个举报。恶意举报可能会影响您的账户信誉。"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:"请选择举报原因："}),(0,a.jsx)("div",{className:"space-y-2",children:h.map(e=>(0,a.jsxs)("label",{className:(0,b.cn)("flex items-center p-3 rounded-lg border cursor-pointer transition-colors",l===e.id?"border-red-500 bg-red-50":"border-gray-200 hover:bg-gray-50"),children:[(0,a.jsx)("input",{type:"radio",name:"reportReason",value:e.id,checked:l===e.id,onChange:()=>c(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:(0,b.cn)("w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center",l===e.id?"border-red-500 bg-red-500":"border-gray-300"),children:l===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-white"})}),(0,a.jsx)("span",{className:(0,b.cn)("text-sm",l===e.id?"text-red-700 font-medium":"text-gray-700"),children:e.label})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(C.Z,{variant:"outline",onClick:x,className:"flex-1",disabled:o,children:"取消"}),(0,a.jsx)(C.Z,{variant:"danger",onClick:m,loading:o,className:"flex-1",disabled:0===l,children:"提交举报"})]})]})})})},I=s(95578),P=s(79994),O=s(59604),z=s(98734),E=s(44995);s(7354),s(76889),s(86968),s(23469);let A=e=>{let t=new Date(e),s=t.getFullYear(),a=t.getMonth()+1,r=t.getDate();return"".concat(s,"/").concat(a,"/").concat(r)};function J(){var e,t,s,_;let C=(0,i.useRouter)(),k=(0,i.useSearchParams)().get("id"),{isLoggedIn:R,user:J}=(0,z.a)(),[D,L]=(0,r.useState)(null),[B,M]=(0,r.useState)(!0),[T,F]=(0,r.useState)(!1),[U,W]=(0,r.useState)(!1),[q,G]=(0,r.useState)(!1),[H,K]=(0,r.useState)(!1),[V,X]=(0,r.useState)(0),[Y,Q]=(0,r.useState)(!1),[$,ee]=(0,r.useState)(!1),[et,es]=(0,r.useState)(!1),[ea,er]=(0,r.useState)(!1),[ei,en]=(0,r.useState)(null),[el,ec]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(!k){C.push("/");return}eo()},[k]);let eo=async()=>{try{M(!0);let n=await N.petAPI.getPostDetail({postId:k});if(n.success){var e,t,s,a,r,i;if(L(n.data),W((null===(e=n.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),G((null===(t=n.data.user_interactions)||void 0===t?void 0:t.hasDisliked)||!1),X((null===(s=n.data.user_interactions)||void 0===s?void 0:s.userRating)||0),(null===(a=n.data.user_interactions)||void 0===a?void 0:a.hasRated)&&(null===(r=n.data.user_interactions)||void 0===r?void 0:r.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(n.data._id)||(e.unshift(n.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}n.data.author&&O.KX.addBrowseRecord(n.data._id,n.data.breed||"宠物发布",n.data.author.nickname||"匿名用户",n.data.author._id,null===(i=n.data.images)||void 0===i?void 0:i[0])}else w.C.error("加载失败："+n.message),C.push("/")}catch(e){console.error("加载帖子详情失败:",e),w.C.error("加载失败，请重试"),C.push("/")}finally{M(!1)}},ed=async()=>{try{let i=await N.petAPI.getPostDetail({postId:k});if(i.success){var e,t,s,a,r;if(L(i.data),W((null===(e=i.data.user_interactions)||void 0===e?void 0:e.hasLiked)||!1),G((null===(t=i.data.user_interactions)||void 0===t?void 0:t.hasDisliked)||!1),X((null===(s=i.data.user_interactions)||void 0===s?void 0:s.userRating)||0),(null===(a=i.data.user_interactions)||void 0===a?void 0:a.hasRated)&&(null===(r=i.data.user_interactions)||void 0===r?void 0:r.userRating)>0)try{let e=JSON.parse(localStorage.getItem("userRatedPosts")||"[]");e.includes(i.data._id)||(e.unshift(i.data._id),e.length>100&&e.splice(100),localStorage.setItem("userRatedPosts",JSON.stringify(e)))}catch(e){console.warn("同步评分记录到本地存储失败:",e)}}}catch(e){console.error("刷新帖子数据失败:",e)}},eu=()=>{F(!T)},ex=async()=>{if(D){if(!R||!J){w.C.error("请先登录");return}if(U||q){w.C.warning("您已经对此帖子做出过评价，无法更改");return}ec({title:"点赞确认",message:"喜欢跟不喜欢只能二选一且不能取消",onConfirm:()=>eh()})}},em=async()=>{if(D){if(!R||!J){w.C.error("请先登录");return}if(U||q){w.C.warning("您已经对此帖子做出过评价，无法更改");return}ec({title:"不喜欢确认",message:"喜欢跟不喜欢只能二选一且不能取消",onConfirm:()=>eg()})}},eh=async()=>{if(D)try{let e=await N.petAPI.addLike({postId:D._id});e.success?(W(!0),L(e=>e?{...e,likes_count:(e.likes_count||0)+1}:null),w.C.success("点赞成功")):w.C.error(e.message||"操作失败")}catch(e){console.error("点赞操作失败:",e),w.C.error("操作失败，请重试")}finally{ec(null)}},eg=async()=>{if(D)try{let e=await N.petAPI.addDislike({postId:D._id});e.success?(G(!0),L(e=>e?{...e,dislikes_count:(e.dislikes_count||0)+1}:null),w.C.success("不喜欢")):w.C.error(e.message||"操作失败")}catch(e){console.error("不喜欢操作失败:",e),w.C.error("操作失败，请重试")}finally{ec(null)}},ef=()=>{let e=window.location.href,t=(null==D?void 0:D.breed)||"宠物交易平台",s="".concat(t," - 来看看这个可爱的宠物吧！");navigator.share?navigator.share({title:t,text:s,url:e}).then(()=>{w.C.success("分享成功")}).catch(t=>{"AbortError"!==t.name&&ev(e,s)}):ev(e,s)},ev=(e,t)=>{let s="".concat(t,"\n").concat(e);navigator.clipboard.writeText(s).then(()=>{w.C.success("分享内容已复制到剪贴板")}).catch(()=>{w.C.error("复制失败，请手动复制链接")})},ep=async e=>{try{let t=await N.petAPI.updateProfile(e);if(t.success)w.C.success("资料更新成功"),e.contactInfo&&J&&localStorage.setItem("contact_".concat(J._id),JSON.stringify(e.contactInfo)),void 0!==e.address&&J&&localStorage.setItem("address_".concat(J._id),e.address);else throw Error(t.message||"更新失败")}catch(e){throw w.C.error(e.message||"更新失败，请重试"),e}},ey=async()=>{if(!R||!J){w.C.error("请先登录");return}let e=null;try{let t=localStorage.getItem("contact_".concat(J._id));t&&(e=JSON.parse(t))}catch(e){console.log("获取联系方式失败")}if(!e||!e.value){w.C.warning("请先完善联系方式才能使用联系功能"),es(!0);return}D&&D.contact_info?ec({title:"联系确认",message:"确定要互相联系对方？点击确定后执行联系方式互换并发送通知",onConfirm:async()=>{try{let t=await N.petAPI.exchangeContact({postId:D._id,contactInfo:e});t.success?w.C.success("联系方式已交换，请查看通知中心"):w.C.error(t.message||"交换失败")}catch(e){console.error("交换联系方式失败:",e),w.C.error("交换失败，请重试")}}}):w.C.error("对方未提供联系方式")};return B?(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"加载中..."})}):D?(0,a.jsxs)("div",{className:"min-h-screen bg-white relative",onClick:()=>K(!1),children:[(0,a.jsxs)("div",{className:(0,b.cn)("fixed top-4 left-4 z-30 flex space-x-2 transition-opacity duration-300",T?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsx)("button",{onClick:()=>C.back(),className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})}),(0,a.jsx)("button",{onClick:eu,className:"w-10 h-10 rounded-full bg-white/90 backdrop-blur-sm border border-gray-200 text-gray-700 flex items-center justify-center hover:bg-gray-100 transition-all duration-200 shadow-sm",title:T?"退出清屏":"清屏模式",children:(0,a.jsx)(l.Z,{className:"h-5 w-5"})})]}),T&&(0,a.jsx)("div",{className:"fixed top-4 left-16 z-30",children:(0,a.jsx)("button",{onClick:eu,className:"w-10 h-10 rounded-full bg-black/50 backdrop-blur-sm border border-white/20 text-white flex items-center justify-center hover:bg-black/70 transition-all duration-200 shadow-sm",title:"退出清屏",children:(0,a.jsx)(c.Z,{className:"h-5 w-5"})})}),(0,a.jsx)("div",{className:"relative h-screen",children:D.images&&D.images.length>0?(0,a.jsx)(y.tq,{modules:[j.tl,j.W_,j.LG],pagination:{clickable:!0},navigation:!0,zoom:{maxRatio:3,minRatio:1,toggle:!0},className:"h-full",children:D.images.map((e,t)=>(0,a.jsx)(y.o5,{children:(0,a.jsxs)("div",{className:"swiper-zoom-container relative h-full",children:[(0,a.jsx)("img",{src:e,alt:"".concat(D.breed||"宠物"," - ").concat(t+1),className:"w-full h-full object-cover"}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none"})]})},t))}):(0,a.jsx)("div",{className:"h-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 text-lg",children:"暂无图片"})})}),(0,a.jsxs)("div",{className:(0,b.cn)("fixed right-4 bottom-20 z-20 flex flex-col space-y-6 transition-opacity duration-300",T?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("button",{onClick:ex,disabled:U||q,className:(0,b.cn)("flex flex-col items-center justify-center transition-all duration-200 group",U||q?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:[(0,a.jsx)(o.Z,{className:(0,b.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",U?"text-red-500 fill-current scale-110":U||q?"text-gray-400":"text-white hover:scale-110")}),(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:D.likes_count||0})]}),(0,a.jsxs)("button",{onClick:em,disabled:U||q,className:(0,b.cn)("flex flex-col items-center justify-center transition-all duration-200 group",U||q?"opacity-50 cursor-not-allowed":"cursor-pointer"),children:[(0,a.jsx)(d.Z,{className:(0,b.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",q?"text-gray-600 fill-current scale-110":U||q?"text-gray-400":"text-white hover:scale-110")}),(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:D.dislikes_count||0})]}),(0,a.jsxs)("button",{onClick:()=>{ed(),Q(!0)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:[(0,a.jsx)(u.Z,{className:(0,b.cn)("h-8 w-8 transition-all duration-200 drop-shadow-lg",V>0?"text-orange-400 fill-current scale-110":"text-white hover:scale-110")}),V>0&&(0,a.jsx)("span",{className:"text-xs mt-1 text-white font-bold drop-shadow-lg",children:V})]}),D&&("breeding"===D.type||"selling"===D.type||"lost"===D.type||"wanted"===D.type)&&(0,a.jsx)("button",{onClick:ey,className:"flex flex-col items-center justify-center transition-all duration-200 group",children:(0,a.jsx)(x.Z,{className:"h-8 w-8 text-blue-400 transition-all duration-200 drop-shadow-lg hover:scale-110"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:e=>{e.stopPropagation(),K(!H)},className:"flex flex-col items-center justify-center transition-all duration-200 group",children:(0,a.jsx)(m.Z,{className:"h-8 w-8 text-white transition-all duration-200 drop-shadow-lg hover:scale-110"})}),H&&(0,a.jsxs)("div",{className:"absolute right-14 bottom-0 w-32 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-30",onClick:e=>e.stopPropagation(),children:[(0,a.jsxs)("button",{onClick:()=>{ef(),K(!1)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(h.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"分享"})]}),(0,a.jsxs)("button",{onClick:()=>{K(!1),ee(!0)},className:"w-full px-4 py-2 text-left text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,a.jsx)(g.Z,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"举报"})]})]})]})]}),(0,a.jsxs)("div",{className:(0,b.cn)("absolute bottom-4 left-4 z-20 bg-black/50 backdrop-blur-md rounded-xl p-4 text-white max-w-sm transition-opacity duration-300 border border-white/10",T?"opacity-0 pointer-events-none":"opacity-100"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(null===(e=D.author)||void 0===e?void 0:e.avatar_url)?(0,a.jsx)("img",{src:D.author.avatar_url,alt:D.author.nickname,className:"w-12 h-12 rounded-full object-cover border-2 border-white/30 shadow-lg"}):(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-600/80 border-2 border-white/30 flex items-center justify-center shadow-lg",children:(0,a.jsx)(f.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(null===(t=D.author)||void 0===t?void 0:t._id)&&"anonymous"!==D.author._id?(0,a.jsx)("button",{onClick:()=>{let e=null;try{let t=localStorage.getItem("pet_platform_user");t&&(e=JSON.parse(t)._id)}catch(e){console.log("获取当前用户信息失败")}e&&D.author&&e===D.author._id?C.push("/profile"):D.author&&C.push("/profile/".concat(D.author._id))},className:"font-semibold text-white hover:text-white/80 transition-colors block truncate text-base",children:(null===(s=D.author)||void 0===s?void 0:s.nickname)||"匿名用户"}):(0,a.jsx)("span",{className:"font-semibold text-white block truncate text-base",children:(null===(_=D.author)||void 0===_?void 0:_.nickname)||"匿名用户"})})]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(v.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{children:A(D.created_at)})]}),D.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:[(0,a.jsx)(p.Z,{className:"h-4 w-4 flex-shrink-0"}),(0,a.jsx)("span",{className:"truncate",children:(0,E.Ed)(D.location)})]}),"breeding"===D.postType&&D.gender&&(0,a.jsx)("div",{className:"flex items-center space-x-2 text-sm text-white/90",children:(0,a.jsx)("span",{className:(0,b.cn)("px-2 py-1 rounded-full text-xs font-medium","male"===D.gender?"bg-blue-500/80 text-white":"bg-pink-500/80 text-white"),children:"male"===D.gender?"雄性":"雌性"})})]}),D.description&&(0,a.jsx)("div",{className:"border-t border-white/20 pt-3",children:(0,a.jsx)("div",{className:"text-sm text-white leading-relaxed",children:(0,a.jsx)("p",{className:"line-clamp-4",children:D.description})})})]}),D&&(0,a.jsx)(S,{isOpen:Y,onClose:()=>Q(!1),post:D,onSuccess:()=>{ed()}}),D&&(0,a.jsx)(Z,{isOpen:$,onClose:()=>ee(!1),postId:D._id,onSuccess:()=>{ee(!1),w.C.success("举报已提交，我们会尽快处理")}}),J&&(0,a.jsx)(I.Z,{isOpen:et,onClose:()=>es(!1),currentUser:J,onUpdate:ep,forceContactTab:!0}),el&&(0,a.jsx)(P.Z,{isOpen:!0,onClose:()=>ec(null),onConfirm:el.onConfirm,title:el.title,message:el.message,type:"info"})]}):(0,a.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-gray-500",children:"宝贝不存在"})})}},56334:function(e,t,s){"use strict";var a=s(57437),r=s(2265),i=s(68661);let n=r.forwardRef((e,t)=>{let{className:s,variant:r="primary",size:n="md",loading:l=!1,icon:c,children:o,disabled:d,...u}=e;return(0,a.jsxs)("button",{className:(0,i.cn)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 active:bg-primary-800",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500 active:bg-gray-300",outline:"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500 active:bg-gray-100",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500 active:bg-gray-200",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 active:bg-red-800",warning:"bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500 active:bg-yellow-800"}[r],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base"}[n],s),ref:t,disabled:d||l,...u,children:[l&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!l&&c&&(0,a.jsx)("span",{className:"mr-2",children:c}),o]})});n.displayName="Button",t.Z=n},79994:function(e,t,s){"use strict";var a=s(57437);s(2265);var r=s(98702),i=s(56334),n=s(63639),l=s(65302),c=s(33245);t.Z=e=>{let{isOpen:t,onClose:s,onConfirm:o,title:d,message:u,confirmText:x="确认",cancelText:m="取消",type:h="info",loading:g=!1}=e;return(0,a.jsx)(r.u_,{isOpen:t,onClose:s,title:"",size:"sm",showCloseButton:!1,children:(0,a.jsx)(r.fe,{children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4",children:(()=>{switch(h){case"danger":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-red-600"});case"warning":return(0,a.jsx)(n.Z,{className:"w-6 h-6 text-yellow-600"});case"success":return(0,a.jsx)(l.Z,{className:"w-6 h-6 text-green-600"});default:return(0,a.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"})}})()}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:d}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-6",children:u}),(0,a.jsxs)("div",{className:"flex space-x-3 justify-center",children:[(0,a.jsx)(i.Z,{variant:"outline",onClick:s,disabled:g,className:"min-w-[80px]",children:m}),(0,a.jsx)(i.Z,{variant:(()=>{switch(h){case"danger":return"danger";case"warning":return"warning";default:return"primary"}})(),onClick:o,loading:g,disabled:g,className:"min-w-[80px]",children:x})]})]})})})}},98702:function(e,t,s){"use strict";s.d(t,{fe:function(){return d},u_:function(){return o}});var a=s(57437),r=s(2265),i=s(54887),n=s(32489),l=s(68661),c=s(56334);let o=e=>{let{isOpen:t,onClose:s,title:o,children:d,size:u="md",showCloseButton:x=!0,closeOnOverlayClick:m=!0,className:h}=e;if((0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,s]),!t)return null;let g=(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:m?s:void 0}),(0,a.jsxs)("div",{className:(0,l.cn)("relative bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-hidden",{sm:"max-w-md",md:"max-w-lg",lg:"max-w-2xl",xl:"max-w-4xl",full:"max-w-full mx-4"}[u],h),onClick:e=>e.stopPropagation(),children:[(o||x)&&(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b border-gray-200",children:[o&&(0,a.jsx)("h2",{className:"text-lg font-semibold text-gray-900",children:o}),x&&(0,a.jsx)(c.Z,{variant:"ghost",size:"sm",onClick:s,className:"p-1 hover:bg-gray-100 rounded-full",children:(0,a.jsx)(n.Z,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(90vh-80px)]",children:d})]})]});return(0,i.createPortal)(g,document.body)},d=e=>{let{children:t,className:s}=e;return(0,a.jsx)("div",{className:(0,l.cn)("p-4",s),children:t})}},44995:function(e,t,s){"use strict";function a(e){if(!e||"string"!=typeof e)return"";let t=e.replace(/^#/,"").trim();if(!t)return"";try{let s="",a="",r="",i=t.match(/(.*?(?:省|自治区|特别行政区))/);i&&(s=i[1],t=t.replace(s,""));let n=t.match(/(.*?市)/);n&&(a=n[1],t=t.replace(a,""));let l=t.match(/(.*?(?:县|区|市|旗|自治县|林区|特区))/);l&&(r=l[1]);let c="",o=["北京","上海","天津","重庆"].some(e=>s&&s.includes(e)||a&&a.includes(e)),d=r&&["浦东新区","滨海新区","两江新区"].some(e=>r.includes(e));if(o?d&&a?c=a+r:r?c=r:a?c=a:s&&(c=s):a&&r?c=a+r:a?c=a:r?c=r:s&&(c=s),!c){let t=e.replace(/^#/,"").trim(),s=t.match(/(.*?(?:镇|乡|街道|办事处))/);if(s)c=s[1];else{let e=t.match(/(.*?(?:村|社区|小区|路|街|巷|弄|号))/);if(e){let t=e[1];c=t.length>8?t.substring(0,8)+"...":t}else c=t.length>10?t.substring(0,10)+"...":t}}return c}catch(e){return console.error("地址格式化出错:",e),t.length>10?t.substring(0,10)+"...":t}}function r(e){return e&&"string"==typeof e?e.replace(/^#/,"").trim():""}s.d(t,{Ed:function(){return r},lx:function(){return a}})}},function(e){e.O(0,[35,649,19,347,554,721,821,11,734,136,971,117,744],function(){return e(e.s=8775)}),_N_E=e.O()}]);