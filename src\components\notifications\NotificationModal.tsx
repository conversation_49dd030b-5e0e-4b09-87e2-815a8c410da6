'use client';

import React, { useState, useEffect } from 'react';
import { X, MessageCircle, Phone, User as UserIcon, ShoppingBag, Heart, Search, Settings, AlertTriangle, CheckCircle, Copy, ExternalLink, Clock } from 'lucide-react';
import { petAPI } from '@/lib/cloudbase';
import { useAuthContext } from '@/components/auth/AuthProvider';
import ConfirmDialog from '@/components/ui/ConfirmDialog';

// 消息类型定义
interface SystemMessage {
  id: string;
  type: 'system';
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  severity: 'info' | 'warning' | 'error';
  canAppeal?: boolean;
  reportId?: string;
  appealStatus?: 'none' | 'pending' | 'approved' | 'rejected';
  notificationType?: 'post_reported' | 'user_reported' | 'general';
  postTitle?: string;
  reportReason?: string;
}

interface ContactMessage {
  id: string;
  type: 'contact';
  fromUser: {
    id: string;
    nickname: string;
    avatar: string;
  };
  toUser: {
    id: string;
    nickname: string;
    avatar: string;
  };
  postInfo: {
    id: string;
    title: string;
    image: string;
    postType: 'selling' | 'breeding' | 'lost' | 'wanted';
  };
  isInitiator: boolean; // 是否是发起联系的用户
  contactInfo: {
    method: 'wechat' | 'phone';
    value: string;
  };
  timestamp: string;
  read: boolean;
}

type Message = SystemMessage | ContactMessage;

interface NotificationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NotificationModal: React.FC<NotificationModalProps> = ({ isOpen, onClose }) => {
  const { user, isLoggedIn } = useAuthContext();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'trade' | 'breeding' | 'lost' | 'system'>('trade');
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [confirmDialogConfig, setConfirmDialogConfig] = useState<{
    title: string;
    message: string;
    onConfirm: () => void;
    confirmText?: string;
    showCancel?: boolean;
    showIcon?: boolean;
  } | null>(null);

  // 加载通知数据
  const loadNotifications = async () => {
    if (!isLoggedIn || !isOpen) return;

    try {
      setLoading(true);
      const result = await petAPI.getUserNotifications({
        limit: 50,
        type: activeTab === 'system' ? undefined : 'contact'
      });

      if (result.success) {
        // 转换为Message格式
        const convertedMessages: Message[] = (result.data || []).map((notification: any) => {
          if (notification.type === 'contact') {
            // 判断当前用户是发起联系的用户A还是接收联系的用户B
            // 如果当前用户是sender，说明是发起联系的用户A
            const isInitiator = notification.sender_id === user?.uid;

            return {
              id: notification._id,
              type: 'contact',
              fromUser: {
                id: notification.sender_id,
                nickname: notification.data?.sender_nickname || notification.data?.author_nickname || '用户',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
              },
              toUser: {
                id: notification.recipient_id,
                nickname: user?.nickname || '',
                avatar: user?.avatar_url || ''
              },
              postInfo: {
                id: notification.post_id,
                title: notification.data?.post_title || '宠物帖子',
                image: 'https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=300&h=300&fit=crop',
                postType: notification.data?.post_type || 'selling'
              },
              contactInfo: {
                method: notification.data?.sender_contact?.type || notification.data?.author_contact?.type || 'wechat',
                value: notification.data?.sender_contact?.value || notification.data?.author_contact?.value || '未提供'
              },
              timestamp: notification.created_at,
              read: notification.read || false,
              isInitiator: isInitiator
            };
          } else {
            // 只处理系统通知，不包括点赞等其他通知
            if (notification.type === 'system' || notification.type === 'punishment' || notification.type === 'appeal') {
              return {
                id: notification._id,
                type: 'system',
                title: notification.message,
                content: notification.message,
                timestamp: notification.created_at,
                read: notification.read || false,
                severity: notification.type === 'punishment' ? 'warning' : 'info',
                canAppeal: notification.type === 'punishment',
                reportId: notification.data?.reportId,
                appealStatus: notification.data?.appealStatus || 'none',
                notificationType: notification.data?.notificationType || 'general',
                postTitle: notification.data?.postTitle,
                reportReason: notification.data?.reportReason
              };
            }
            return null;
          }
        }).filter(Boolean);

        setMessages(convertedMessages);
      } else {
        setMessages([]);
      }
    } catch (error) {
      console.error('加载通知失败:', error);
      setMessages([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      loadNotifications();
    }
  }, [isOpen, isLoggedIn, activeTab]);

  // 标记为已读
  const markAsRead = async (messageId: string) => {
    try {
      await petAPI.markNotificationRead({ notificationId: messageId });
      setMessages(prev => prev.map(msg =>
        msg.id === messageId ? { ...msg, read: true } : msg
      ));
    } catch (error) {
      console.error('标记已读失败:', error);
    }
  };

  // 删除单个通知
  const handleDeleteMessage = async (messageId: string) => {
    if (deletingId) return; // 防止重复点击

    setDeletingId(messageId);

    try {
      await petAPI.deleteNotification({ notificationId: messageId });
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
    } catch (error) {
      console.error('删除失败:', error);
      // 使用模拟框提示替代alert
      setConfirmDialogConfig({
        title: '操作失败',
        message: '删除失败，请稍后重试',
        onConfirm: () => setConfirmDialogConfig(null),
        confirmText: '确定',
        showCancel: false,
        showIcon: false
      });
    } finally {
      setDeletingId(null);
    }
  };

  // 清空当前分类的所有通知
  const handleClearCategory = async () => {
    const confirmMessage = {
      'trade': '确定清空所有买卖通知吗？',
      'breeding': '确定清空所有配种通知吗？',
      'lost': '确定清空所有寻回通知吗？',
      'system': '确定清空所有系统通知吗？'
    };

    // 使用模拟框提示替代网页弹窗
    setConfirmDialogConfig({
      title: '清空通知',
      message: confirmMessage[activeTab],
      showIcon: false, // 不显示图标
      onConfirm: async () => {
        try {
          // 获取当前分类的消息ID
          const idsToDelete = filteredMessages.map(msg => msg.id);

          if (idsToDelete.length === 0) return;

          // 使用批量删除API
          await petAPI.bulkDeleteNotifications({ notificationIds: idsToDelete });

          // 从列表中移除这些消息
          setMessages(prev => prev.filter(msg => !idsToDelete.includes(msg.id)));

          console.log(`已清空${filteredMessages.length}条通知`);
        } catch (error) {
          console.error('清空失败:', error);
          // 使用模拟框提示替代alert
          setConfirmDialogConfig({
            title: '操作失败',
            message: '清空失败，请稍后重试',
            onConfirm: () => setConfirmDialogConfig(null),
            confirmText: '确定',
            showCancel: false,
            showIcon: false
          });
        }
      }
    });
  };

  // 过滤通知
  const filteredMessages = messages.filter(message => {
    if (activeTab === 'system') {
      return message.type === 'system';
    }
    if (activeTab === 'breeding') {
      return message.type === 'contact' && (message as ContactMessage).postInfo.postType === 'breeding';
    }
    if (activeTab === 'lost') {
      return message.type === 'contact' && (message as ContactMessage).postInfo.postType === 'lost';
    }
    if (activeTab === 'trade') {
      return message.type === 'contact' &&
             ((message as ContactMessage).postInfo.postType === 'selling' ||
              (message as ContactMessage).postInfo.postType === 'wanted');
    }
    return true;
  });

  // 获取联系方式图标
  const getContactIcon = (type: string) => {
    switch (type) {
      case 'phone':
        return <Phone className="h-4 w-4" />;
      case 'wechat':
        return <MessageCircle className="h-4 w-4" />;
      case 'qq':
        return <UserIcon className="h-4 w-4" />;
      default:
        return <MessageCircle className="h-4 w-4" />;
    }
  };

  // 获取联系方式标签
  const getContactLabel = (type: string) => {
    switch (type) {
      case 'phone':
        return '📱';
      case 'wechat':
        return '💬';
      case 'qq':
        return '🐧';
      default:
        return '💬';
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 flex-shrink-0">
          <h2 className="text-xl font-semibold text-gray-900">通知中心</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="h-5 w-5 text-gray-500" />
          </button>
        </div>

        {/* 标签页 */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 m-4 flex-shrink-0">
          <button
            onClick={() => setActiveTab('trade')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'trade'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <ShoppingBag className="w-4 h-4" />
            <span>买卖通知</span>
          </button>
          <button
            onClick={() => setActiveTab('breeding')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'breeding'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Heart className="w-4 h-4" />
            <span>配种通知</span>
          </button>
          <button
            onClick={() => setActiveTab('lost')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'lost'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Search className="w-4 h-4" />
            <span>寻回通知</span>
          </button>
          <button
            onClick={() => setActiveTab('system')}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'system'
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Settings className="w-4 h-4" />
            <span>系统通知</span>
          </button>
        </div>

        {/* 清空按钮 - 优化垂直居中布局 */}
        {!loading && filteredMessages.length > 0 && (
          <div className="flex justify-end px-4 py-3 flex-shrink-0">
            <button
              onClick={handleClearCategory}
              className="px-4 py-2 text-sm text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
            >
              清空当前分类
            </button>
          </div>
        )}

        {/* 通知列表 */}
        <div className="flex-1 overflow-y-auto px-6 pt-3 pb-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              <span className="ml-2 text-gray-600">加载中...</span>
            </div>
          ) : filteredMessages.length === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">暂无通知</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredMessages.map((message) => (
                <div
                  key={message.id}
                  className={`bg-white rounded-lg border p-4 hover:shadow-md transition-shadow cursor-pointer ${
                    !message.read ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => markAsRead(message.id)}
                >
                  {message.type === 'system' ? (
                    <SystemMessageCard
                      message={message as SystemMessage}
                      onDelete={handleDeleteMessage}
                      deletingId={deletingId}
                    />
                  ) : (
                    <ContactMessageCard
                      message={message as ContactMessage}
                      onDelete={handleDeleteMessage}
                      deletingId={deletingId}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* 确认对话框 */}
      {confirmDialogConfig && (
        <ConfirmDialog
          isOpen={true}
          onClose={() => setConfirmDialogConfig(null)}
          onConfirm={() => {
            confirmDialogConfig.onConfirm();
            if (confirmDialogConfig.showCancel !== false) {
              setConfirmDialogConfig(null);
            }
          }}
          title={confirmDialogConfig.title}
          message={confirmDialogConfig.message}
          confirmText={confirmDialogConfig.confirmText || '确定'}
          type="info"
          showIcon={confirmDialogConfig.showIcon}
        />
      )}
    </div>
  );
};

// 联系消息卡片组件
const ContactMessageCard: React.FC<{
  message: ContactMessage;
  onDelete: (id: string) => void;
  deletingId: string | null;
}> = ({ message, onDelete, deletingId }) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return `${year}年${month}月${day}日${hour}时${minute.toString().padStart(2, '0')}分`;
  };

  // 判断当前用户是发起联系的用户A还是接收联系的用户B
  const isInitiator = message.isInitiator; // 需要在数据转换时设置这个字段

  const handleViewPost = () => {
    window.open(`/pet-trading-platform/post/detail/?id=${message.postInfo.id}`, '_blank');
  };

  const handleCopyContact = () => {
    navigator.clipboard.writeText(message.contactInfo.value);
    // 可以添加复制成功的提示
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-3">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-500">
            {formatTime(message.timestamp)}
          </span>
          {!message.read && (
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          )}
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(message.id);
          }}
          disabled={deletingId === message.id}
          className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
        >
          {deletingId === message.id ? (
            <div className="w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
          ) : (
            <X className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* 通知内容 */}
      <div className="space-y-2">
        {/* 第一行：联系状态信息 */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-900">
            {isInitiator ? (
              <>已与<span className="font-medium">{message.fromUser.nickname}</span>取得联系：{message.contactInfo.value}</>
            ) : (
              <>用户<span className="font-medium">{message.fromUser.nickname}</span>想与您取得联系 {message.contactInfo.value}</>
            )}
          </p>
          <button
            onClick={handleCopyContact}
            className="text-xs bg-blue-500 text-white px-3 py-1 rounded-full hover:bg-blue-600 transition-colors flex-shrink-0 ml-2"
          >
            复制
          </button>
        </div>

        {/* 第二行：帖子信息 */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-gray-700">
            {message.postInfo.title}
          </p>
          <button
            onClick={handleViewPost}
            className="text-xs text-blue-600 hover:text-blue-800 transition-colors flex-shrink-0 ml-2"
          >
            查看帖子
          </button>
        </div>
      </div>
    </div>
  );
};

// 系统消息卡片组件
const SystemMessageCard: React.FC<{
  message: SystemMessage;
  onDelete: (id: string) => void;
  deletingId: string | null;
}> = ({ message, onDelete, deletingId }) => {
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hour = date.getHours();
    const minute = date.getMinutes();

    return `${year}年${month}月${day}日${hour}时${minute.toString().padStart(2, '0')}分`;
  };

  const getSystemIcon = (severity: string) => {
    switch (severity) {
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-orange-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <CheckCircle className="w-5 h-5 text-blue-500" />;
    }
  };

  // 格式化系统通知内容
  const formatSystemMessage = () => {
    if (message.notificationType === 'post_reported') {
      return `您发布的宝贝${message.postTitle || ''}被人多次举报已经隐藏，举报理由为：${message.reportReason || '违规内容'}。如要申诉请点击右边的申诉按钮。`;
    } else if (message.notificationType === 'user_reported') {
      return '您被人多次举报涉嫌诈骗，如要申诉请点击右边申诉按钮。';
    }
    return message.content;
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 mb-3">
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0 mt-1">
          {getSystemIcon(message.severity)}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">
                {formatTime(message.timestamp)}
              </span>
              {!message.read && (
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              )}
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete(message.id);
              }}
              disabled={deletingId === message.id}
              className="p-1 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
            >
              {deletingId === message.id ? (
                <div className="w-4 h-4 animate-spin rounded-full border-2 border-red-500 border-t-transparent"></div>
              ) : (
                <X className="w-4 h-4" />
              )}
            </button>
          </div>

          {/* 系统通知内容 */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-900 flex-1">
              {formatSystemMessage()}
            </p>

            {/* 申诉按钮 */}
            {message.canAppeal && (
              <div className="ml-3 flex-shrink-0">
                {message.appealStatus === 'none' && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      // 这里可以添加申诉逻辑
                    }}
                    className="text-sm bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    申诉
                  </button>
                )}
                {message.appealStatus === 'pending' && (
                  <span className="text-sm text-orange-600 bg-orange-100 px-3 py-1 rounded-full">
                    申诉处理中...
                  </span>
                )}
                {message.appealStatus === 'approved' && (
                  <span className="text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full">
                    申诉已通过
                  </span>
                )}
                {message.appealStatus === 'rejected' && (
                  <span className="text-sm text-red-600 bg-red-100 px-3 py-1 rounded-full">
                    申诉已驳回
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
