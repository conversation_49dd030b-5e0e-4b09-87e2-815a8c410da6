(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[57],{68399:function(e,s,t){Promise.resolve().then(t.bind(t,9537))},9537:function(e,s,t){"use strict";t.r(s);var r=t(57437),i=t(2265),l=t(88997),a=t(41473),c=t(7586),n=t(47465),d=t(89841),x=t(56334),o=t(88941),m=t(98011),g=t(31215),h=t(9356),u=t(68661);s.default=()=>{let{user:e,isLoggedIn:s}=(0,o.E)(),[t,p]=(0,i.useState)([]),[j,N]=(0,i.useState)(!0),[f,y]=(0,i.useState)("grid"),b=async()=>{try{N(!0);let e=await m.petAPI.getUserBookmarks({limit:50});e.success&&p(e.data||[])}catch(e){console.error("获取收藏列表失败:",e),h.C.error("获取收藏列表失败")}finally{N(!1)}};return(0,i.useEffect)(()=>{b()},[]),(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(n.Z,{}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(l.Z,{className:"h-6 w-6 text-red-500 mr-2"}),"我的收藏"]}),(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[t.length," 个收藏"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>y("grid"),className:(0,u.cn)("p-2 rounded-lg transition-colors","grid"===f?"bg-primary-600 text-white":"bg-white text-gray-600 hover:bg-gray-100"),children:(0,r.jsx)(a.Z,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>y("list"),className:(0,u.cn)("p-2 rounded-lg transition-colors","list"===f?"bg-primary-600 text-white":"bg-white text-gray-600 hover:bg-gray-100"),children:(0,r.jsx)(c.Z,{className:"h-4 w-4"})})]})]}),j?(0,r.jsx)("div",{className:(0,u.cn)("grid"===f?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4":"space-y-4"),children:Array.from({length:8}).map((e,s)=>(0,r.jsx)(g.gG,{},s))}):t.length>0?(0,r.jsx)("div",{className:(0,u.cn)("grid"===f?"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4":"space-y-4"),children:t.map(e=>(0,r.jsx)(d.Z,{post:e,showRemoveFromFavorites:!0,onRemoveFromFavorites:()=>{p(s=>s.filter(s=>s._id!==e._id)),h.C.success("已从收藏中移除")}},e._id))}):(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsxs)("div",{className:"text-gray-500",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDC9D"}),(0,r.jsx)("p",{className:"text-lg font-medium mb-2",children:"还没有收藏任何宠物"}),(0,r.jsx)("p",{className:"text-sm mb-6",children:"去首页看看有什么喜欢的宠物吧"}),(0,r.jsx)(x.Z,{onClick:()=>window.location.href="/",children:"去首页逛逛"})]})})]})]})}}},function(e){e.O(0,[649,19,347,554,721,319,39,11,734,825,465,971,117,744],function(){return e(e.s=68399)}),_N_E=e.O()}]);