{"envId": "yichongyuzhou-3g9112qwf5f3487b", "version": "2.0", "framework": {"name": "pet-trading-platform", "plugins": {"client": {"use": "@cloudbase/framework-plugin-website", "inputs": {"buildCommand": "npm run build", "outputPath": "out", "cloudPath": "/pet-trading-platform"}}, "server": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "cloudfunctions", "functions": [{"name": "pet-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "description": "宠物交易平台主要API - 包含联系方式交换、通知管理等核心功能"}, {"name": "user-auth", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256}]}}}}, "functions": [{"name": "pet-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}, {"name": "user-auth", "timeout": 60, "envVariables": {}, "runtime": "Nodejs18.15", "memorySize": 256, "handler": "index.main"}], "database": {"collections": [{"collectionName": "users", "description": "用户信息表", "aclTag": "READONLY"}, {"collectionName": "posts", "description": "宠物帖子表", "aclTag": "READONLY"}, {"collectionName": "categories", "description": "分类表", "aclTag": "READONLY"}, {"collectionName": "likes", "description": "点赞表", "aclTag": "READONLY"}, {"collectionName": "wants", "description": "想买表", "aclTag": "READONLY"}, {"collectionName": "ratings", "description": "评分表", "aclTag": "READONLY"}, {"collectionName": "follows", "description": "关注表", "aclTag": "READONLY"}, {"collectionName": "post_reports", "description": "帖子举报表", "aclTag": "ADMINONLY"}, {"collectionName": "user_reports", "description": "用户举报表", "aclTag": "ADMINONLY"}, {"collectionName": "notifications", "description": "通知表 - 包含联系方式交换通知等", "aclTag": "PRIVATE"}, {"collectionName": "favorites", "description": "收藏表", "aclTag": "PRIVATE"}, {"collectionName": "messages", "description": "消息表", "aclTag": "PRIVATE"}]}, "lastUpdate": "2025-01-29", "version_info": {"description": "宠物交易平台 - 2025-01-29 最新版本", "features": ["联系方式交换功能完整修复", "通知中心界面优化", "复制功能增强", "删除通知持久化修复", "确认对话框自动关闭"], "fixes": ["修复联系确认对话框不自动关闭的问题", "修复通知中心清空按钮布局问题", "完全重构联系方式交换逻辑", "修复通知内容显示'未提供'的问题", "增强复制按钮功能并添加成功提示", "修复删除通知后重新出现的问题"], "deployment": {"frontend": "https://yichongyuzhou-3g9112qwf5f3487b-1368816056.tcloudbaseapp.com/pet-trading-platform/", "functions": ["pet-api"], "status": "deployed"}}}